<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6082_187142)">
<g filter="url(#filter0_biii_6082_187142)">
<path d="M10.498 16.6673C14.1799 16.6673 17.1647 13.6825 17.1647 10.0007C17.1647 6.31875 14.1799 3.33398 10.498 3.33398" stroke="#CFD1D9" stroke-width="6.66667"/>
</g>
<g filter="url(#filter1_biii_6082_187142)">
<path d="M10.498 3.33464C6.81615 3.33464 3.83138 6.3194 3.83138 10.0013C3.83138 13.6832 6.81615 16.668 10.498 16.668" stroke="#CFD1D9" stroke-width="6.66667"/>
</g>
</g>
<defs>
<filter id="filter0_biii_6082_187142" x="1.74805" y="-8.74951" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_187142"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_187142" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_187142"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_187142" result="effect3_innerShadow_6082_187142"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_187142" result="effect4_innerShadow_6082_187142"/>
</filter>
<filter id="filter1_biii_6082_187142" x="-8.25195" y="-8.74854" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_187142"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_187142" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_187142"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_187142" result="effect3_innerShadow_6082_187142"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_187142" result="effect4_innerShadow_6082_187142"/>
</filter>
<clipPath id="clip0_6082_187142">
<rect width="20" height="20" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>

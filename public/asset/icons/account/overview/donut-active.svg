<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6082_187132)">
<g filter="url(#filter0_biii_6082_187132)">
<path d="M9.99805 16.6668C13.6799 16.6668 16.6647 13.6821 16.6647 10.0002C16.6647 6.31826 13.6799 3.3335 9.99805 3.3335" stroke="#DA1A12" stroke-width="6.66667"/>
</g>
<g filter="url(#filter1_biii_6082_187132)">
<path d="M9.99805 3.33366C6.31615 3.33366 3.33138 6.31843 3.33138 10.0003C3.33138 13.6822 6.31615 16.667 9.99805 16.667" stroke="#DA1A12" stroke-width="6.66667"/>
</g>
</g>
<defs>
<filter id="filter0_biii_6082_187132" x="1.24805" y="-8.75" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_187132"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_187132" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_187132"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_187132" result="effect3_innerShadow_6082_187132"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_187132" result="effect4_innerShadow_6082_187132"/>
</filter>
<filter id="filter1_biii_6082_187132" x="-8.75195" y="-8.74951" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_187132"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_187132" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_187132"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_187132" result="effect3_innerShadow_6082_187132"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_187132" result="effect4_innerShadow_6082_187132"/>
</filter>
<clipPath id="clip0_6082_187132">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>

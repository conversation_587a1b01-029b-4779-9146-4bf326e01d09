<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6188_186860)">
<path opacity="0.8" d="M5.45508 12.0005C5.45508 8.38558 8.38558 5.45508 12.0005 5.45508C15.6155 5.45508 18.546 8.38558 18.546 12.0005C18.546 15.6155 15.6155 18.546 12.0005 18.546C8.38558 18.546 5.45508 15.6155 5.45508 12.0005Z" fill="#FCA5A5"/>
<g filter="url(#filter0_biii_6188_186860)">
<path d="M1.08984 12.0009C1.08984 5.97596 5.97401 1.0918 11.9989 1.0918C18.0239 1.0918 22.908 5.97596 22.908 12.0009C22.908 18.0258 18.0239 22.91 11.9989 22.91C5.97401 22.91 1.08984 18.0258 1.08984 12.0009Z" fill="white"/>
</g>
<g filter="url(#filter1_biii_6188_186860)">
<path d="M12 22.364C17.7237 22.364 22.3636 17.724 22.3636 12.0004C22.3636 6.27668 17.7237 1.63672 12 1.63672" stroke="#EF4444" stroke-width="3"/>
</g>
<g filter="url(#filter2_biii_6188_186860)">
<path d="M12 1.63601C6.27632 1.63601 1.63636 6.27596 1.63636 11.9996C1.63636 17.7233 6.27632 22.3633 12 22.3633" stroke="#EF4444" stroke-width="3"/>
</g>
<path d="M13.8302 6.81818V17H11.6775V8.86151H11.6178L9.28613 10.3232V8.41406L11.8067 6.81818H13.8302Z" fill="#DC2626"/>
</g>
<defs>
<filter id="filter0_biii_6188_186860" x="-7.50107" y="-7.49911" width="39.0002" height="39.0002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.29545"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6188_186860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6188_186860" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84091"/>
<feGaussianBlur stdDeviation="0.920455"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.45455"/>
<feGaussianBlur stdDeviation="1.22727"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6188_186860" result="effect3_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.613636"/>
<feGaussianBlur stdDeviation="0.306818"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6188_186860" result="effect4_innerShadow_6188_186860"/>
</filter>
<filter id="filter1_biii_6188_186860" x="3.40909" y="-8.45419" width="29.0451" height="40.9089" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.29545"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6188_186860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6188_186860" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84091"/>
<feGaussianBlur stdDeviation="0.920455"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.45455"/>
<feGaussianBlur stdDeviation="1.22727"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6188_186860" result="effect3_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.613636"/>
<feGaussianBlur stdDeviation="0.306818"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6188_186860" result="effect4_innerShadow_6188_186860"/>
</filter>
<filter id="filter2_biii_6188_186860" x="-8.45419" y="-8.45468" width="29.0451" height="40.9089" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.29545"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6188_186860"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6188_186860" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.84091"/>
<feGaussianBlur stdDeviation="0.920455"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.45455"/>
<feGaussianBlur stdDeviation="1.22727"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6188_186860" result="effect3_innerShadow_6188_186860"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.613636"/>
<feGaussianBlur stdDeviation="0.306818"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6188_186860" result="effect4_innerShadow_6188_186860"/>
</filter>
<clipPath id="clip0_6188_186860">
<rect width="24" height="24" rx="9.81818" fill="white"/>
</clipPath>
</defs>
</svg>

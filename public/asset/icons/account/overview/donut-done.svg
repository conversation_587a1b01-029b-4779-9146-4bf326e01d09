<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6082_168900)">
<g filter="url(#filter0_biii_6082_168900)">
<path d="M9.99805 16.6668C13.6799 16.6668 16.6647 13.6821 16.6647 10.0002C16.6647 6.31826 13.6799 3.3335 9.99805 3.3335" stroke="#DA1A12" stroke-width="6.66667"/>
</g>
<g filter="url(#filter1_biii_6082_168900)">
<path d="M9.99805 3.33366C6.31615 3.33366 3.33138 6.31843 3.33138 10.0003C3.33138 13.6822 6.31615 16.667 9.99805 16.667" stroke="#DA1A12" stroke-width="6.66667"/>
</g>
<g filter="url(#filter2_biii_6082_168900)">
<circle cx="10" cy="10" r="5" stroke="#DA1A12" stroke-width="10"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.6834 6.97644C15.1055 7.3897 15.1055 8.05971 14.6834 8.47297L9.54822 13.4994C9.12604 13.9127 8.44153 13.9127 8.01934 13.4994L5.31664 10.8539C4.89445 10.4407 4.89445 9.77065 5.31664 9.3574C5.73883 8.94414 6.42333 8.94414 6.84552 9.3574L8.78378 11.2546L13.1545 6.97644C13.5767 6.56319 14.2612 6.56319 14.6834 6.97644Z" fill="white"/>
</g>
<defs>
<filter id="filter0_biii_6082_168900" x="1.24805" y="-8.75" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_168900"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_168900" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_168900" result="effect3_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_168900" result="effect4_innerShadow_6082_168900"/>
</filter>
<filter id="filter1_biii_6082_168900" x="-8.75195" y="-8.74951" width="27.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_168900"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_168900" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_168900" result="effect3_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_168900" result="effect4_innerShadow_6082_168900"/>
</filter>
<filter id="filter2_biii_6082_168900" x="-8.75" y="-8.75" width="37.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.375"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6082_168900"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6082_168900" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6082_168900" result="effect3_innerShadow_6082_168900"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_6082_168900" result="effect4_innerShadow_6082_168900"/>
</filter>
<clipPath id="clip0_6082_168900">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>

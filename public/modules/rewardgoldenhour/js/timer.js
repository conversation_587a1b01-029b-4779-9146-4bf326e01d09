window.addEventListener('DOMContentLoaded', () => {
    // ================ Timer handler ================
    const now = new Date().toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' });
    const currentTime = new Date(now);
    const goldenHourTimerWrapper = document.querySelectorAll('.js-golden-hour-timer-wrapper');
    const goldenHourNoTime = document.querySelector('.js-golden-hour-no-time');

    const dayTimers = document.querySelectorAll('.js-golden-hour-timer-day');
    const hourTimers = document.querySelectorAll('.js-golden-hour-timer-hour');
    const minuteTimers = document.querySelectorAll('.js-golden-hour-timer-minute');
    const secondTimers = document.querySelectorAll('.js-golden-hour-timer-second');
    
    // If globalGoldenHourEventStartTime is not defined, set it to current date
    const goldenHourEventStartTime = !!globalGoldenHourEventStartTime
        ? new Date(globalGoldenHourEventStartTime)
        : new Date(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate(), 0, 0, 0);

    // Check if current time is before event end time
    const goldenHourEventEndTime = !!globalGoldenHourEventEndTime
        ? new Date(globalGoldenHourEventEndTime)
        : null;

    const isEventNotYetStarted = currentTime < goldenHourEventStartTime;
    const isEventOpening = goldenHourEventEndTime ? (goldenHourEventStartTime <= currentTime && currentTime <= goldenHourEventEndTime) : false;

    if (isEventNotYetStarted) {
        goldenHourNoTime.classList.remove('hidden');
        goldenHourTimerWrapper.forEach(wrapper => {
            wrapper.classList.add('hidden');
        });
        
        return;
    }

    if (!isEventOpening) {
        goldenHourNoTime.classList.add('hidden');
        goldenHourTimerWrapper.forEach(wrapper => {
            wrapper.classList.remove('hidden');
        });

        const timerContent = document.querySelectorAll('.js-golden-hour-timer-content');
        timerContent.forEach(content => {
            content.innerHTML = 'SỰ KIỆN ĐÃ KẾT THÚC';
        });

        return;
    }

    if (isEventOpening) {
        goldenHourNoTime.classList.add('hidden');
        goldenHourTimerWrapper.forEach(wrapper => {
            wrapper.classList.remove('hidden');
        });
        
        function updateCountdown() {
            const now = new Date().toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' });
            const currentTime = new Date(now);
            const timeLeft = goldenHourEventEndTime - currentTime;

            if (timeLeft <= 0) {
                goldenHourTimer.classList.add('hidden');
                return;
            }

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            // Update the countdown content
            dayTimers.forEach(timer => {
                timer.innerHTML = `${days} <span>Ngày</span>`;
            });
            hourTimers.forEach(timer => {
                timer.innerHTML = `${hours.toString().padStart(2, '0')} <span class="ml-[2px]">Giờ</span>`;
            });
            minuteTimers.forEach(timer => {
                timer.innerHTML = `${minutes.toString().padStart(2, '0')} <span class="ml-[2px]">Phút</span>`;
            });
            // secondTimers.forEach(timer => {
            //     timer.innerHTML = `${seconds.toString().padStart(2, '0')}<span class="ml-[2px]">G</span>`;
            // });
        }

        updateCountdown();
        setInterval(updateCountdown, 1000);
    }
});

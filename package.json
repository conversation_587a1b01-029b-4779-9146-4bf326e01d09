{"name": "fe", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "convert:image": "node convert-and-compress-images.js"}, "devDependencies": {"autoprefixer": "^10.4.20", "gitlog": "^5.1.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.47", "rollup-plugin-terser": "^7.0.2", "tailwindcss": "^3.4.14", "vite": "^5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-purgecss": "^0.2.12"}, "dependencies": {"@alpinejs/anchor": "^3.14.1", "@alpinejs/collapse": "^3.14.1", "sass": "^1.81.0", "sharp": "^0.34.1", "swiper": "^11.2.7", "toastify-js": "^1.12.0"}, "resolutions": {"cross-spawn": "^7.0.5", "nanoid": "^3.3.8"}}
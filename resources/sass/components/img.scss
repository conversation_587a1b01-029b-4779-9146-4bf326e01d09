// Optimized skeleton loader with better performance
.skeleton-loader {
    position: relative;
    background-image: url('/public/asset/images/games/game-loading-bg.avif');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    // Use will-change for better animation performance
    will-change: opacity;

    &::before {
        content: '';
        position: absolute;
        width: 66px;
        height: 66px;
        background-image: url('/public/asset/images/games/game-loading.avif');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        animation: rotate 2s linear infinite;
        // Optimize animation performance
        will-change: transform;
        transform-origin: center;
    }
}

// Optimized rotation animation
@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

// Improved skeleton animation
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    will-change: background-position;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Optimized visibility classes
.hidden {
    display: none !important;
}

// Improved lazy image loading with transitions
.lazy-image {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    will-change: opacity;

    &.loaded {
        opacity: 1;
    }

    // Fallback for browsers without Intersection Observer
    &:not([data-src]) {
        opacity: 1;
    }
}

// Optimize image containers
.image-container {
    position: relative;
    overflow: hidden;

    img {
        width: 100%;
        height: auto;
        // Improve image rendering
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

// Critical CSS for above-the-fold images
.priority-image {
    .lazy-image {
        opacity: 1;
    }

    .skeleton {
        display: none;
    }
}

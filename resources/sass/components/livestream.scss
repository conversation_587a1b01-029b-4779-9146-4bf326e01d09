// To fix live loading game overlap other elements
[id^="middleView-"] {
    z-index: 1 !important;
}

[id^="h5live-"] {
    pointer-events: none !important;
}

[id^="clickWrapper-"] {
    pointer-events: none !important;
}

// To fix Safari cannot click on the game
.video-wrapper {
    video, iframe {
        pointer-events: none !important;
        object-fit: cover !important;
        width: 102% !important;
        height: 102% !important;
        color-scheme: light !important;
    }
}

// To fix Safari scale live stream
.game-container {
    .video-wrapper {
        video {
            transform: translate(-50%,-50%) scaleX(1) scaleY(1) rotate(0)!important
        }
        video#h5live-vingame_bc_77784{
            transform: translate(-36%,-34%) scaleX(1.3) scaleY(1.3) rotate(0)!important;
        }
        video, iframe {
            pointer-events: none !important;
            object-fit: cover !important;
            width: 102% !important;
            height: 102% !important;
            color-scheme: light;
        }
        iframe {
            @media screen and (max-width: 991px) {
                transform: scale(2) !important;
            }
        }
    }
}


.js-live-game-item {
    z-index: 4;
    video {
        @apply w-full h-full relative;
        transform: translate(-50%, -50%) scaleX(1.05) scaleY(1.05) rotate(0deg) !important;
    }
    video {
        @media screen and (min-width: 992px) {
            transform: translate(-50%, -50%) scaleX(1.05) scaleY(1.05) rotate(0deg) !important;
        }
        @media screen and (max-width: 991px) {
            transform: translate(-50%, -50%) scaleX(2) scaleY(2) rotate(0deg) !important;
        }
    }
    video, iframe {
        pointer-events: none !important;
        object-fit: cover !important;
        width: 102% !important;
        height: 102% !important;
        color-scheme: light;
    }
    iframe {
        @media screen and (max-width: 991px) {
            transform: scale(2) !important;
        }
    }

    div {
        img {
            z-index: 1;
            transform: translate(-50%, -50%) scaleX(1) scaleY(1) rotate(0deg) !important;
            object-fit: cover !important;
            pointer-events: none;
        }
    }
}

.js-live-game-list-mb,
.js-live-game-list {
    .js-live-game-item {
        img {
            transform: unset !important;
            object-fit: cover !important;
            margin: 0 !important;
            top: 0 !important;
            left: 0 !important;
            object-fit: contain !important;
        }
    }
}
.js-home-live-game-list{
    video#h5live-vingame_bc_77784 {
        transform: translate(-50%,-29%) scaleX(1.4) scaleY(1.4) rotate(0)!important;
    }
}

.js-live-game-list-mb {
    .js-live-game-item {
        iframe {
            transform: scale(1.1) !important;
        }
    }
}

.js-game-card-item {
    .jackpot-wrapper {
        z-index: 5;
    }
    .js-game-card-group {
        z-index: 4;
    }
    .card-label {
        z-index: 5;
    }
}

.live-game-square {
    video {
        transform: translate(-50%, -50%) scaleX(1.05) scaleY(1.05) rotate(0deg) !important;
    }
    // To fix Safari scale live stream
    iframe {
        transform: translate(0, 0) scaleX(2) scaleY(2) rotate(0deg) !important;
    }
}

.js-live-game-list-mb,
.js-live-game-list {
    .js-live-game-item {
        img {
            transform: unset !important;
            object-fit: cover !important;
        }
        @media (max-width: 1199px) {
            img:not(.main-thumbnail) {
                transform: unset !important;
            }
        }

        @media (max-width: 1199px) {
            video {
                transform: translate(-50%, -50%) scaleX(1.05) scaleY(1.05) rotate(0) !important;
            }
        }
    }
}

// Live stream radio wave animation
.live-indicator {
    display: flex;
    align-items: center;
    gap: var(--live-gap, 8px);

    .live-dot {
        position: relative;
        width: var(--live-dot-size, 10px);
        height: var(--live-dot-size, 10px);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .live-dot::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        width: var(--live-dot-inner-size, 6px);
        height: var(--live-dot-inner-size, 6px);
        background: #f71b26;
        border-radius: 50%;
        z-index: 2;
        box-shadow: 0 0 4px 1px #ff2d2d80;
        transform: translate(-50%, -50%);
    }
    
    .wave {
        position: absolute;
        left: 50%;
        top: 50%;
        width: var(--live-dot-size, 10px);
        height: var(--live-dot-size, 10px);
        border-radius: 50%;
        border: var(--live-dot-wave-size, 0.5px) solid #ff2d2d;
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0.7;
        z-index: 1;
        animation: wave-anim 3s linear infinite;
    }
    
    .wave:nth-child(1) { animation-delay: 0s; }
    .wave:nth-child(2) { animation-delay: 2s; }
    
    @keyframes wave-anim {
        0% {
            transform: translate(-50%, -50%) scale(0.7);
            opacity: 0.7;
        }
        70% {
            opacity: 0.2;
        }
        100% {
            transform: translate(-50%, -50%) scale(1.5);
            opacity: 0;
        }
    }
    
    .live-text {
        display: flex;
        font-size: var(--live-text-size, 16px);
        color: #fff;
        letter-spacing: var(--live-letter-spacing, 1px);
    }
    
    .live-text span,
    .live-text span.run-anim {
        animation: live-text-anim 4s forwards;
        animation-fill-mode: forwards;
        transition: filter 0.4s linear;
        filter: blur(2px);
    }
    
    .live-text span:nth-child(1) { animation-delay: 0s; }
    .live-text span:nth-child(2) { animation-delay: 0.4s; }
    .live-text span:nth-child(3) { animation-delay: 0.8s; }
    .live-text span:nth-child(4) { animation-delay: 1.2s; }
    
    @keyframes live-text-anim {
        0% {
            opacity: 0;
            transform: translateX(100px);
            filter: blur(2px);
        }
        60% {
            opacity: 0;
            transform: translateX(40px);
            filter: blur(1px);
        }
        80% {
            opacity: 1;
            transform: translateX(0);
            filter: blur(0);
        }
        100% {
            opacity: 1;
            transform: translateX(0);
            filter: blur(0);
        }
    }
}

.home-live-jackpot-label {
    background: linear-gradient(90deg, #FCDB87 0%, #FFB700 50%, #FFD56A 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

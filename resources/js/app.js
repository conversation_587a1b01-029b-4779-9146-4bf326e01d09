import "./socket";
import "./modal";
import "./sports";
import "./auth";
import "./toast";
import "./dropdown-new";
import "./add-bank";
import "./validation";
import "./format-value";
import "./games";
import "./input";
import "./common/number";
import "./common/copy";
import "./google-recaptcha";
import "./affiliate";

import Swiper from "swiper";
import { Autoplay, Pagination, Navigation, Grid } from "swiper/modules";
import 'swiper/css/bundle';
import 'swiper/bundle';

Swiper.use([Autoplay, Pagination, Navigation, Grid]);
window.Swiper = Swiper;
// import $ from '/public/js/jquery.min.js'; // Adjust the path based on where you saved jQuery
// window.$ = window.jQuery = $; // Make jQuery available globally

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const apiVer = import.meta.env.VITE_API_VER;

export function debounce(func, delay = 500) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
window.debounce = debounce;

export function getQueryParamsFromSearchParams(keys = []) {
    const urlParams = new URLSearchParams(window.location.search);

    if (keys.length > 0) {
        return keys.reduce(
            (queryParams, key) => ({
                ...queryParams,
                [key]: urlParams.get(key) ?? "",
            }),
            {}
        );
    }

    return {
        filter: urlParams.get("filter") ?? "",
        type: urlParams.get("type") ?? "",
        keyword: urlParams.get("keyword") ?? "",
        p: urlParams.get("p") ?? "",
    };
}

window.getQueryParamsFromSearchParams = getQueryParamsFromSearchParams;

export function pushState(newState = {}) {
    const activeFilter = {
        ...getQueryParamsFromSearchParams(["filter", "type", "keyword", "p"]),
        ...newState,
    };

    const url = new URL(location);

    Object.entries(activeFilter).forEach(([key, value]) => {
        url.searchParams.set(key, value);
    });

    window.history.pushState(
        {
            path: url.href,
            ...activeFilter,
        },
        "",
        url.href
    );

    window.dispatchEvent(
        new CustomEvent("pushstate", {
            detail: {
                path: url.href,
                state: activeFilter,
            },
        })
    );
}

window.pushState = pushState;

window.closeModalById = (id) => {
    const modal = document.getElementById(id);
    if (modal) {
        modal.remove();
    }
};

function getCookies() {
    if (!document.cookie) return {};

    return document.cookie
        .split(";")
        .filter((cookie) => cookie.trim())
        .reduce((cookies, cookie) => {
            try {
                const [name, ...valueParts] = cookie
                    .split("=")
                    .map((part) => part.trim());
                const value = valueParts.join("=");

                if (name && value !== undefined) {
                    cookies[decodeURIComponent(name)] =
                        decodeURIComponent(value);
                }
                return cookies;
            } catch (e) {
                console.warn("Error parsing cookie:", cookie, e);
                return cookies;
            }
        }, {});
}

window.getCookies = getCookies;

export async function fetchData(
    url,
    params,
    options = {},
    apiBase = apiBaseUrl,
    ver = apiVer
) {
    const { useAjax = false, useProxy = true } = options;
    const baseUrl = useProxy ? apiBase + ver : apiBase;
    let response;
    const query = new URLSearchParams(params).toString();
    const _headers = getCookies();
    const csrfToken = document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");
    response = await fetch(
        !!query ? `${baseUrl}${url}?${query}` : `${baseUrl}${url}`,
        {
            method: "GET",
            credentials: "include",
            headers: {
                ..._headers,
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "X-CSRF-TOKEN": csrfToken,
                "accept-language": "vi",
            },
        }
    );

    response = await response.json();

    if (response.status !== "OK") {
        console.error(response.message);
    }
    return response;
}
window.fetchData = fetchData;

export async function submitData(
    url,
    params,
    apiBase = apiBaseUrl,
    ver = apiVer
) {
    const baseUrl = apiBase + ver;
    const _headers = getCookies();

    const csrfToken = document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");

    let response = await fetch(`${baseUrl}${url}`, {
        method: "POST",
        credentials: "include",
        headers: {
            ..._headers,
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "X-CSRF-TOKEN": csrfToken,
            "accept-language": "vi",
        },
        body: JSON.stringify(params),
    });

    response = await response.json();
    if (!response.status) {
        console.log(response.message);
    }
    return response;
}
window.submitData = submitData;

function deleteCookie(name) {
    document.cookie =
        name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}

window.deleteCookie = deleteCookie;

// Format amount helper function
window.formatAmount = (value) => {
    if (!value) return "0";
    const numbers = value.toString().replace(/\D/g, "");
    return numbers.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

window.formatSerial = (value) => {
    if (!value) return "";
    return value.toString().replace(/\D/g, "");
};

window.formatPhone = (value) => {
    if (!value) return "";
    return value.toString().replace(/\D/g, "");
};

window.getOldJackpotValue = (value) => {
    if (!value) return 0;
    const length = value.toString().length;
    const unit = Math.pow(10, Math.max(0, length - 3));
    return Math.floor(value / unit) * unit;
};

window.animateCounter = (
    element,
    oldValue,
    newValue,
    duration = 5000,
    prefix = ""
) => {
    oldValue = roundDownToNearest(newValue);
    const distance = Math.abs(newValue - oldValue);
    const length = distance.toString().length;

    const numberOfSteps = length > 1 ? Math.pow(10, length - 1) : 1;
    const stepSize = Math.ceil(distance / numberOfSteps);

    $(element)
        .prop("Counter", oldValue)
        .animate(
            {
                Counter: newValue,
            },
            {
                duration: duration,
                step: function (now, fx) {
                    if (fx.pos > 0 && fx.pos < 1) {
                        const delay = 100;
                        const lastUpdate = $(this).data("lastUpdate") || 0;
                        const currentTime = Date.now();

                        if (currentTime - lastUpdate < delay) {
                            return;
                        }
                        $(this).data("lastUpdate", currentTime);
                    }

                    const stepValue = Math.floor(now / stepSize) * stepSize;
                    if (stepValue < newValue) {
                        $(this).text(
                            `${formatAmount(stepValue || 0)} ${
                                prefix || $(this).data("prefix") || ""
                            }`
                        );
                    } else {
                        $(this).text(
                            `${formatAmount(newValue || 0)} ${
                                prefix || $(this).data("prefix") || ""
                            }`
                        );
                    }
                },
            }
        );
};

window.isMobile = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
        /(android|ipad|playbook|silk|mobile|tablet|iphone|ipod|blackberry|iemobile|opera mini)/i.test(
            ua
        );

    const hasTouch =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

    const isMobileSize = window.innerWidth <= 1024;

    return isMobileUA || (hasTouch && isMobileSize);
};

window.isSafari = () => {
    const ua = navigator.userAgent.toLowerCase();

    return ua.indexOf("safari") !== -1 && ua.indexOf("chrome") === -1;
};

const openChangeName = () => {
    openModal(changeNameModal, false, "change-name-modal");
    handleInput();
    bindChangeNameForm();
};

const openLogin = (redirect) => {
    openAuthModal(loginModal, false, "login-modal");
    handleInput();
    bindLoginForm(redirect);
};

const openSignup = () => {
    openAuthModal(signupModal, false, "signup-modal");
    handleInput();
    bindSignupForm();
};

const openForgetPass = () => {
    openAuthModal(forgetModal, false, "forget-modal");
    handleInput();
    bindForgetForm();
};

const cancelPromotion = async () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if (user && user.plan_id) {
        try {
            const { status, data } = await submitData('/payment/cancelpromotion', {}, '');
            if (status === 'OK') {
                const { status: dataStatus, message } = data
                if (dataStatus !== 'ERROR') {
                    openNotiModal(
                        'Hủy khuyến mãi thành công',
                        '',
                        '',
                        'Đóng',
                        '/asset/images/popup/success-cancel-promotion.avif',
                        () => {
                            window.location.reload();
                        } ,
                        () => {
                            window.location.reload();
                        },
                        '',
                        true,
                        () => {
                            window.location.reload();
                        },
                        true,
                        true
                    );

                    return;
                }

                openNotiModal(
                    'Hủy khuyến mãi thất bại',
                    message,
                    'Đóng',
                    'Xem khuyến mãi',
                    '/asset/images/popup/error-cancel-promotion.avif',
                    () => {
                        closeNotificationModal();
                    },
                    () => {
                        window.location.href = '/account/promotion';
                    },
                    '',
                    true,
                    () => {
                        closeNotificationModal();
                    },
                    true,
                    true
                )
            } else {
                openNotiModal(
                    'Hủy khuyến mãi thất bại',
                    data.message,
                    'Đóng',
                    'Xem khuyến mãi',
                    '/asset/images/popup/error-cancel-promotion.avif',
                    () => {
                        closeNotificationModal();
                    },
                    () => {
                        window.location.href = '/account/promotion';
                    },
                    '',
                    true,
                    () => {
                        closeNotificationModal();
                    },
                    true,
                    true
                );

                return;
            }
        } catch (error) {
            openNotiModal(
                'Hủy khuyến mãi thất bại',
                error.message,
                'Đóng',
                'Xem khuyến mãi',
                '/asset/images/popup/error-cancel-promotion.avif',
                () => {
                    closeNotificationModal();
                },
                () => {
                    window.location.href = '/account/promotion';
                },
                '',
                true,
                () => {
                    closeNotificationModal();
                },
                true,
                true
            );
        }
    }
}

window.openChangeName = openChangeName;
window.openLogin = openLogin;
window.openSignup = openSignup;
window.openForgetPass = openForgetPass;
window.cancelPromotion = cancelPromotion;

window.addEventListener("DOMContentLoaded", () => {
    if (!window.location.pathname.includes("account/withdraw")) {
        localStorage.removeItem("withdraw-promotion");
    }
    const checkMinigameDisplay = () => {
        if ($(".gamebox").css("display") === "block") {
            $("body").css("overflow", "hidden");
        } else {
            $("body").css("overflow", "");
        }
    };

    // Initial check
    checkMinigameDisplay();

    // Observe changes to the minigame element
    const observer = new MutationObserver(checkMinigameDisplay);
    const minigameElement = document.querySelector(".gamebox");
    if (minigameElement) {
        observer.observe(minigameElement, {
            attributes: true,
            attributeFilter: ["style"],
        });
    }

    const cookies = getCookies();
    let refreshInterval;
    let isRefreshing = false;

    if (cookies && cookies.user) {
        refreshInterval = setInterval(async () => {
            if (isRefreshing) {
                return;
            }

            try {
                isRefreshing = true;

                const { user = {}, status } = await fetchData(
                    "/refresh",
                    {},
                    {},
                    "",
                    ""
                );

                if (status !== "OK") {
                    if (refreshInterval) {
                        clearInterval(refreshInterval);
                        refreshInterval = null;
                    }
                    deleteCookie("user");
                    openNotiModal(
                        "Phiên đăng nhập hết hạn",
                        "Vui lòng đăng nhập lại để tiếp tục trải nghiệm",
                        "",
                        "Đăng Nhập",
                        "/asset/images/popup/img-user-error.avif",
                        () => {},
                        () => {
                            window.location.href = '/?type=modal-login';
                        }
                    );
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                }

                const { balance_txt } = user;
                $(".js-user-balance").html(
                    `${formatNumberWithComma(balance_txt) ?? 0}<span>K</span>`
                );
            } catch (error) {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                }
            } finally {
                isRefreshing = false;
            }
        }, 5000);
    }
});

window.getSlugsFromUrl = () => {
    const pathSegments = window.location.pathname
        .split("/")
        .filter((segment) => segment);
    return {
        page: pathSegments[0] || "",
        type: pathSegments[1] || "",
    };
};

const toggleGameItem = (target, isDisabled) => {
    if (!target) return;
    $(target).closest('.js-game-item-wrapper').toggleClass('!pointer-events-none', isDisabled);
};

const disabledGameItem = (target) => toggleGameItem(target, true);
const enabledGameItem = (target) => setTimeout(toggleGameItem(target, false), 300);

window.disabledGameItem = disabledGameItem
window.enabledGameItem = enabledGameItem

window.addFavoriteGame = () => {
    $(".js-game-favorite").off("click");
    $(".js-game-favorite").on("click", async function (event) {
        event.preventDefault();
        event.stopPropagation();
        $(this).addClass("pointer-events-none");
        disabledGameItem(this);
        const cookies = getCookies();
        if (!cookies || !cookies.user) {
            openLogin();
            $(this).removeClass("pointer-events-none");
            enabledGameItem(this)
            return;
        }
        const icon = $(this).find("i");
        const loadingIcon = $(this).find(".loading-icon");
        const isFavorite = icon?.hasClass("icon-favorited");
        const pathname = window.location.pathname;
        const slug = pathname.split("/")[1];

        icon.toggleClass("hidden");
        loadingIcon.toggleClass("hidden");

        const type = $(this).data("type");
        const res = await submitData(
            `/${type}/${
                icon.hasClass("icon-favorited") ? "unfavorite" : "favorite"
            }`,
            {
                gId: String($(this).data("game-id")),
                name: $(this).data("name"),
                p: $(this).data("provider"),
                ...(type === "casino" && { tId: String($(this).data("table-id") ?? '') })
            },
            ""
        );
        $(this).removeClass("pointer-events-none");
        enabledGameItem(this);
        if (res.status === "OK") {
            if (isFavorite) {
                $(this).find("i").removeClass("text-primary");
                $(this).find("i").addClass("text-neutral");
            } else {
                $(this).find("i").removeClass("text-neutral");
                $(this).find("i").addClass("text-primary");
            }
            $(this).find("i").toggleClass("icon-favorited");
            $(this).find("i").toggleClass("icon-unfavorite");

            const { type: typeSlug } = getSlugsFromUrl();
            if (typeSlug === "favorite") {
                $(this).closest(".js-game-card-item").remove();
                const count = parseInt($(".js-current-game-count").text()) - 1;
                const total = parseInt($(".js-game-total").text()) - 1;
                if (count <= 0) {
                    // Move to all provider page
                    const currentPage = window.location.href.includes('/cong-game')
                        ? '/cong-game/favorite'
                        : '/song-bai-livecasino-truc-tuyen/favorite';
                    window.location.href = currentPage;
                } else {
                    if (total % 20 === 0) {
                        $(".js-game-total").text(total > 0 ? total : 0);
                        window.location.reload();
                    }
                    $(".js-current-game-count").text(count);
                    $(".js-game-total").text(total > 0 ? total : 0);
                }
            }

            if (typeSlug === "favorite") {
                const res = await fetchData(
                    `/${type === "game" ? "game/provider" : "casino/provider"}`,
                    {},
                    { useAjax: true },
                    ""
                );
                if (res.status === "OK" && res.data) {
                    const favoriteProvider = res.data.favorite;
                    if (favoriteProvider.length <= 1) {
                        $(".provider-dropdown").addClass("opacity-0");
                        return;
                    }
                    $(".provider-dropdown .dropdown-list li").each(function () {
                        const $this = $(this);
                        if ($this.hasClass("active")) {
                            return;
                        }
                        if (
                            !favoriteProvider.some(
                                (provider) =>
                                    provider.key === $this.data("value")
                            ) &&
                            $this.data("value") !== "all"
                        ) {
                            $this.addClass("hidden");
                        }
                    });
                }
            }
        } else {
            useToast("error", res?.message);
        }

        icon.toggleClass("hidden");
        loadingIcon.toggleClass("hidden");
    });
};

window.openNotiModal = (
    title,
    content,
    buttonCancel,
    buttonConfirm,
    img = "",
    onCancel = () => {},
    onConfirm = () => {},
    reload = "",
    isCloseAfterConfirm = false,
    onClose = () => {},
    isCloseAfterCancel = false,
    isSticky = false
) => {
    openModal(notiModal, isSticky, "noti-modal", false, reload, onClose);
    if (title) {
        $(".js-popup-title").html(title);
    }
    if (content) {
        $(".js-popup-content").html(content);
    }
    if (img) {
        $(".js-img-popup-error").attr("src", img);
    }
    if (buttonCancel) {
        $(".js-popup-button-cancel").text(buttonCancel);
    } else {
        $(".js-popup-button-group")
            .removeClass("grid-cols-2")
            .addClass("grid-cols-1");
        $(".js-popup-button-cancel").remove();
    }
    if (buttonConfirm) {
        $(".js-popup-button-confirm").text(buttonConfirm);
    }

    $(".js-popup-button-cancel").on("click", async () => {
        if (onCancel) {
            await onCancel();
        }
        if (!isCloseAfterCancel) {
            closeModal();
        }
    });

    $(".js-popup-button-confirm").on("click", () => {
        if (onConfirm) {
            onConfirm();
        }
        if (!isCloseAfterConfirm) {
            closeModal();
        }
    });
};

window.addLabel = () => {
    $(".js-game-card-item").each(function () {
        const data = $(this).data();
        if (data?.tags) {
            if (data?.tags === "new") {
                $(this).find(".card-label").html(labelNew);
            } else if (data?.tags === "hot") {
                $(this).find(".card-label").html(labelHot);
            } else if (data?.tags === "live") {
                $(this).find(".card-label").html(labelLive);
            } else if (data?.tags === "event") {
                $(this).find(".card-label").html(labelEvent);
            }
        } else {
            $(this).find(".card-label").remove();
        }
    });
};

const urlParams = new URLSearchParams(window.location.search);
if (urlParams.get("type") === "modal-login") {
    setTimeout(() => {
        openLogin();
    }, 200);

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);
}
if (urlParams.get("type") === "modal-register") {
    setTimeout(() => {
        openSignup();
    }, 200);

    urlParams.delete("type");
    const newUrl = `${window.location.origin}${window.location.pathname}${
        urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState(null, "", newUrl);
}
window.checkLabel = () => {
    $(".js-game-card-item").each(function () {
        const data = $(this).data();
        if (!data?.tags) {
            $(this).find(".card-label").remove();
        }
    });
};

window.getQueryParamsCurrentUrl = () => {
    const url = window.location.href;
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    return params;
};

window.loadExternalScript = (
    url,
    callback,
    type,
    options = {
        async: true,
        defer: true,
        crossorigin: "",
    }
) => {
    const script = document.createElement("script");
    if (options.async) {
        script.async = options.async;
    }
    if (type) {
        script.type = type;
    }
    if (options.defer) {
        script.defer = options.defer;
    }
    if (options.crossorigin) {
        script.crossorigin = options.crossorigin;
    }
    script.src = url;
    if (callback) {
        script.addEventListener("load", callback, false);
    }
    if (document) {
        document.body.appendChild(script);
    }
};

function openLiveStream(playerId = "") {
    var player;
    var middleViewEl;
    var defaultUrl = "rtmp://bintu-splay.nanocosmos.de/splay";
    var defaultServer = {
        websocket: "wss://bintu-h5live-secure.nanocosmos.de/h5live/authstream",
        hls: "https://bintu-h5live-secure.nanocosmos.de/h5live/authhttp/playlist.m3u8",
    };
    var isPlaying = true;
    var isMute = true;
    var ip = "";
    var showIP = true;
    var countRetry = 0;
    var maxRetry = 3;

    // var params = new URL(window.location.href).searchParams
    // var token = params.get('token')
    // var enableSound = params.get('enableSound') || null
    var enableSound = null;

    function verifyToken(divId = "", groupId, streamName, site) {
        try {
            const videoThumbnail = $(`#${divId}`).data('thumbnail');
            var config = {
                source: {
                    entries: [
                        {
                            h5live: {
                                server: defaultServer,
                                rtmp: {
                                    url: defaultUrl,
                                    streamname: "",
                                },
                                security: {
                                    jwtoken: "",
                                },
                            },
                        },
                    ],
                },
                playback: {
                    autoplay: true,
                    automute: true,
                    muted: true,
                    faststart: true,
                },
                style: {
                    displayMutedAutoplay: false,
                    controls: false,
                    fullScreenControl: false,
                    interactive: true,
                    poster: videoThumbnail,
                },
                events: {
                    onSwitchStreamSuccess: function (e) {},
                    onReady: function (e) {
                        showIconSound(divId);
                        if (isMobile()) {
                            $(".js-mb-img-video-custom")
                                .removeClass("block")
                                .addClass("hidden");
                        } else {
                            $(".js-pc-img-video-custom")
                                .removeClass("xl:block")
                                .addClass("xl:hidden");
                        }

                        middleViewEl = document.getElementById(
                            "middleView-" + divId
                        );
                        $(".js-live-game-item-preview")
                            .find("div")
                            .addClass("pointer-events-none");
                        $(".js-live-game-item-preview")
                            .find("video")
                            .addClass("pointer-events-none");
                        $(".js-live-game-item-preview")
                            .find("iframe")
                            .addClass("pointer-events-none");
                        const gameItemEl = document.getElementById(divId);
                        if (gameItemEl) {
                            const thumbnailEl =
                                gameItemEl.querySelector(".img-video-custom");
                            if (thumbnailEl) {
                                thumbnailEl.style.display = "none";
                            }
                        }
                        middleViewEl.ontouchmove = function (event) {
                            event.preventDefault();
                        };
                        if (middleViewEl) {
                            middleViewEl.style.zIndex = "10";
                        }
                    },
                    onPlay: function (e) {
                        showIconSound(divId);
                        // $('.img-video-custom').addClass('hidden');
                        if (isMute) {
                            soundOff();
                            if (
                                enableSound == null ||
                                enableSound == undefined ||
                                enableSound == "true"
                            ) {
                                // soundOn();
                                var soundoff =
                                    document.getElementById("soundoff");
                                // soundoff.click();
                            }
                        } else {
                            // soundOn();
                        }

                        isPlaying = true;
                    },
                    onLoading: function (e) {},
                    onStartBuffering: function (e) {},
                    onStopBuffering: function (e) {},
                    onError: function (e) {
                        hideIconSound(divId);
                        const gameItemEl = document.getElementById(divId);
                        if (isMobile()) {
                            $(".js-mb-img-video-custom")
                                .removeClass("hidden")
                                .addClass("block");
                        } else {
                            $(".js-pc-img-video-custom")
                                .removeClass("xl:hidden")
                                .addClass("xl:block");
                        }
                        if (gameItemEl) {
                            const thumbnailEl =
                                gameItemEl.querySelector(".img-video-custom");
                            if (thumbnailEl) {
                                thumbnailEl.style.display = "";
                                thumbnailEl.style.zIndex = "5";
                            }
                            gameItemEl.style.background = "transparent";
                            gameItemEl.style.backgroundColor = "transparent";
                        }
                        middleViewEl = document.getElementById(
                            "middleView-" + divId
                        );
                        if (middleViewEl) {
                            middleViewEl.style.display = "none";
                            middleViewEl.style.zIndex = "10";
                        }
                    },
                    onMute: function (e) {
                        /*if (isPlaying) {
                        setSoundOffEl();
                    }
                    isMute = true;*/
                        isMute = true;
                    },
                    onUnmute: function (e) {
                        /*if (isPlaying) {
                        setSoundOnEl();
                    }
                    isMute = false;*/
                        isMute = false;
                    },
                    onPause: function (e) {
                        /*hideSoundEl();
                    if (e.data != null && e.data != undefined && e.data.reason != null && e.data.reason != undefined && e.data.reason !== "playbacksuspended") {
                        window.location.reload();
                    }
                    isPlaying = false;*/
                    },
                },
            };

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function () {
                if (this.readyState == 4 && this.status == 200) {
                    let result = JSON.parse(this.response);
                    let data = result.data;
                    //let streamId = result.streamId;

                    if (data != null && data != undefined) {
                        let jwtoken = data.token;
                        if (jwtoken != null && jwtoken != undefined) {
                            config.source.entries[0].h5live.security.jwtoken =
                                jwtoken;
                        }
                    }
                    config.source.entries[0].h5live.rtmp.streamname =
                        streamName;
                    player = new NanoPlayer(divId);
                    player.setup(config).then(
                        function (config) {},
                        function (error) {}
                    );

                    ip = result.ip;
                    if (ip != null && ip != undefined && showIP) {
                        document.getElementById(ipId).textContent = ip;
                    }
                }
            };
            xhttp.onerror = function () {
                setTimeout(function () {
                    retryVerifyToken(divId, groupId, streamName, site);
                }, 500);
            };
            xhttp.ontimeout = function () {
                setTimeout(function () {
                    retryVerifyToken(divId, groupId, streamName, site);
                }, 500);
            };
            xhttp.open(
                "POST",
                "https://api-csn-s.gameland.today/api/v1/stream/",
                true
            );
            xhttp.setRequestHeader("Content-type", "application/json");
            xhttp.send(
                JSON.stringify({
                    groupId: groupId,
                    streamId: streamName,
                    site: site,
                })
            );

            var videoEl = document.getElementById("h5live-" + divId);
            if (videoEl) {
                videoEl.ontouchmove = function (event) {
                    event.preventDefault();
                };
            }
            if (blocktouchEl) {
                var blocktouchEl = document.getElementById(
                    "blocktouch-" + divId
                );
                blocktouchEl.ontouchmove = function (event) {
                    event.preventDefault();
                };
                blocktouchEl.ontouchstart = function (event) {
                    event.preventDefault();
                };
            }

            var soundOffEl = document.getElementById("soundoff");
            if (soundOffEl) {
                soundOffEl.ontouchmove = function (event) {
                    event.preventDefault();
                };
            }

            var soundOnEl = document.getElementById("soundon");
            if (soundOnEl) {
                soundOnEl.ontouchmove = function (event) {
                    event.preventDefault();
                };
            }
        } catch (error) {
            console.log("error", error);
        }
    }

    function showIconSound(divId = "") {
        if (window && window.innerWidth < 1199) {
            $(`#${divId}`)?.closest('.js-game-card-item')?.find(".js-toggle-sound")?.removeClass("hidden")?.addClass("flex");
        }
    }

    function hideIconSound(divId = "") {
        $(`#${divId}`)?.closest('.js-game-card-item')?.find(".js-toggle-sound")?.removeClass("flex")?.addClass("hidden");
    }

    function soundOn() {
        player?.unmute();
    }

    function soundOff() {
        player?.mute();
    }

    function setSoundOnEl() {
        soundOffEl.style.display = "none";
        soundOnEl.style.display = "block";
    }

    function setSoundOffEl() {
        soundOffEl.style.display = "block";
        soundOnEl.style.display = "none";
    }

    function hideSoundEl() {
        soundOffEl.style.display = "none";
        soundOnEl.style.display = "none";
    }

    function retryVerifyToken(divId, groupId, streamName, site) {
        countRetry++;
        if (countRetry <= maxRetry) {
            verifyToken(divId, groupId, streamName, site);
        } else {
            const gameItemEl = document.getElementById(divId);
            if (gameItemEl) {
                const thumbnailEl =
                    gameItemEl.querySelector(".img-video-custom");
                if (thumbnailEl) {
                    thumbnailEl.style.display = "";
                    thumbnailEl.style.zIndex = "5";
                }
                gameItemEl.style.background = "transparent";
                gameItemEl.style.backgroundColor = "transparent";
            }
        }
    }

    const tokenMap = new Map([
        [
            "rik_vgmn_108",
            { id: "41b93f00-3f85-4008-86e2-8e297e6799aa", key: "XpjSI-uWCBa" },
        ],
        [
            "rik_vgmn_109",
            { id: "867f07d4-2a67-4aa0-9c9a-306489ac3ca5", key: "XpjSI-Xl7Hj" },
        ],
        [
            "rik_vgmn_110",
            { id: "dbfca645-f428-4157-858d-a52a7fd026e3", key: "XpjSI-PkUx8" },
        ],
        [
            "rik_vgmn_111",
            { id: "da3844de-812d-446d-b1e9-158eb10819c4", key: "XpjSI-t7no7" },
        ],
        [
            "go_qs_txgo-101",
            { id: "360d8af8-5d64-43df-9bd9-fa91ad6f9c60", key: "XpjSI-X3Chu" },
        ],
        [
            "go_vgmn_109",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "b52_vgmn_108",
            { id: "f61fbb0a-2595-450e-ae70-19b04bdc5710", key: "XpjSI-5emz7" },
        ],
        [
            "b52_vgmn_109",
            { id: "ff9599e8-8cc4-40a2-ae0a-6f6c43984ccc", key: "XpjSI-vlkTG" },
        ],
        [
            "b52_vgmn_110",
            { id: "946b8871-2b8d-4dfd-9fe7-4ffd835fe98a", key: "XpjSI-h2HI7" },
        ],
        [
            "rik_vgmn_108_mobile",
            { id: "41b93f00-3f85-4008-86e2-8e297e6799aa", key: "XpjSI-uWCBa" },
        ],
        [
            "rik_vgmn_109_mobile",
            { id: "867f07d4-2a67-4aa0-9c9a-306489ac3ca5", key: "XpjSI-Xl7Hj" },
        ],
        [
            "rik_vgmn_110_mobile",
            { id: "dbfca645-f428-4157-858d-a52a7fd026e3", key: "XpjSI-PkUx8" },
        ],
        [
            "rik_vgmn_111_mobile",
            { id: "da3844de-812d-446d-b1e9-158eb10819c4", key: "XpjSI-t7no7" },
        ],
        [
            "go_qs_txgo-101_mobile",
            { id: "360d8af8-5d64-43df-9bd9-fa91ad6f9c60", key: "XpjSI-X3Chu" },
        ],
        [
            "go_vgmn_109_mobile",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "go_qs_xocdia-102",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "go_qs_xocdia-102_mobile",
            { id: "9291199a-50c5-434b-9e97-5aeb670927d1", key: "XpjSI-MsM1Y" },
        ],
        [
            "b52_vgmn_108_mobile",
            { id: "f61fbb0a-2595-450e-ae70-19b04bdc5710", key: "XpjSI-5emz7" },
        ],
        [
            "b52_vgmn_109_mobile",
            { id: "ff9599e8-8cc4-40a2-ae0a-6f6c43984ccc", key: "XpjSI-vlkTG" },
        ],
        [
            "b52_vgmn_110_mobile",
            { id: "946b8871-2b8d-4dfd-9fe7-4ffd835fe98a", key: "XpjSI-h2HI7" },
        ],
        [
            "sunwin_G1S_305",
            { id: "7652d5a1-11fd-4f52-bb74-933631fd78df", key: "7MhcV-Pi0uG" },
        ],
        [
            "sunwin_G1S_305_mobile",
            { id: "7652d5a1-11fd-4f52-bb74-933631fd78df", key: "7MhcV-Pi0uG" },
        ],
        [
            "sunwin_G1S_306",
            { id: "c88ff431-0ee8-4faf-b375-1b515b20ae68", key: "7MhcV-ZvYvo" },
        ],
        [
            "sunwin_G1S_306_mobile",
            { id: "c88ff431-0ee8-4faf-b375-1b515b20ae68", key: "7MhcV-ZvYvo" },
        ],
        [
            "789club_G1X_305",
            { id: "f23545d4-bbb0-416f-bbb7-972619813f2", key: "7MhcV-weX7L" },
        ],
        [
            "789club_G1X_305_mobile",
            { id: "f23545d4-bbb0-416f-bbb7-972619813f2", key: "7MhcV-weX7L" },
        ],
        [
            "789club_G1X_306",
            { id: "fd2186c3-26f3-40ac-9864-4287052b0ec3", key: "7MhcV-fwQ2N" },
        ],
        [
            "789club_G1X_306_mobile",
            { id: "fd2186c3-26f3-40ac-9864-4287052b0ec3", key: "7MhcV-fwQ2N" },
            "vingame_bc_77784",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "vingame_bc_77784_mobile",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "vingame_bc_77784",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "vingame_bc_77784_mobile",
            { id: "c639fc58-878b-414c-9ba3-9536088d430a", key: "XpjSI-h4o0U" },
        ],
        [
            "vingame_sb_77783",
            { id: "9cb5fda4-cd6c-4279-a9d6-eefa396b2d92", key: "XpjSI-gzDej" },
        ],
        [
            "vingame_sb_77783_mobile",
            { id: "9cb5fda4-cd6c-4279-a9d6-eefa396b2d92", key: "XpjSI-gzDej" },
        ],
    ]);
    const key = playerId ? playerId : $("#playerDiv").data("id");
    const token = tokenMap.get(key);

    if (token && playerId) {
        verifyToken(
            playerId,
            token.id || "",
            token.key,
            window.location.hostname
        );
    }
    // else {
    //     if (isMobile()) {
    //         $('.js-mb-img-video-custom').removeClass('hidden').addClass('block');
    //     } else {
    //         $('.js-pc-img-video-custom').removeClass('xl:hidden').addClass('xl:block');
    //     }
    // }
    if (playerId) {
        $(`#${playerId}`)
            ?.closest(".js-game-item-wrapper")
            ?.find(".js-toggle-sound")
            ?.on("click", function (event) {
                event.stopPropagation();
                event.preventDefault();
                try {
                    if (isMute) {
                        soundOn();
                        $(this)
                            .find("i")
                            .removeClass("icon-unvolume")
                            .addClass("icon-volum");
                    } else {
                        soundOff();
                        $(this)
                            .find("i")
                            .removeClass("icon-volum")
                            .addClass("icon-unvolume");
                    }
                } catch (error) {}
            });
    }
}
window.openLiveStream = openLiveStream;

const openCancelPromotionModal = () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if (
        user &&
        user.plan_id &&
        [2, 3, 4].includes(user.package_id)
    ) {
        const promotionTitle = promotionList.find((item)=>item.id===user.package_id)?.title || '';
        openNotiModal(
            "Thông báo",
            `Quý khách vui lòng huỷ hoặc hoàn thành giá trị nạp gói ${promotionTitle} trước khi nạp tiền`,
            "Hủy khuyến mãi",
            "Trang chủ",
            "/asset/images/account/confirm-cancel-promotion.svg",
            () => {
                openNotiModal(
                    "Hủy khuyến mãi",
                    "Khi bấm huỷ khuyến mãi, tài khoản của quý khách sẽ bị trừ tiền (Bao gồm tiền khuyến mãi và tiền thắng cược)",
                    "Hủy khuyến mãi",
                    "Chi tiết khuyến mãi",
                    "/asset/images/popup/cancel-promotion.avif",
                    async () => {
                      await cancelPromotion();
                    },
                    () => {
                      window.location.href = "/account/promotion";
                    },
                    "",
                    true,
                    () => {},
                    true,
                    true,
                );
            },
            () => {
                window.location.href = "/";
            },
            '',
            true,
            () => {},
            true,
            true
        );
        return true;
    }
    return false;
};

window.openCancelPromotionModal = openCancelPromotionModal;

function encodeForDataAttr(obj) {
    const json = JSON.stringify(obj)
      .replace(/[\u007F-\uFFFF]/g, c =>
        '\\u' + ('0000' + c.charCodeAt(0).toString(16)).slice(-4)
      ) // Unicode escape
      .replace(/"/g, '&quot;') // HTML entity escape
      .replace(/\//g, '\\/');  // Escape slash (optional)

    return json;
  }

window.encodeForDataAttr = encodeForDataAttr;

// Tạo một kênh broadcast
const channel = new BroadcastChannel('reload_channel');

// Lắng nghe tín hiệu từ các tab khác
channel.onmessage = (event) => {
  if (event.data === 'reload') {
    location.reload();
  }
};

// Phát tín hiệu reload đến các tab khác
function triggerReloadForAllTabs() {
  channel.postMessage('reload');
  location.reload();
}

window.triggerReloadForAllTabs = triggerReloadForAllTabs;

window.addEventListener("pageshow", function (event) {
    if (event?.persisted || (window?.performance && window?.performance?.navigation?.type === 2)) {
        window.location.reload();
    }
});

// Modern Intersection Observer based lazy loading
class LazyImageLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                // Load images when they're 50px away from viewport
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeImages();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('.lazy-image[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const skeleton = img.parentElement.querySelector('.skeleton');
        const dataSrc = img.getAttribute('data-src');
        const errorSrc = img.getAttribute('data-error');

        if (dataSrc) {
            img.onload = () => {
                // Use requestAnimationFrame for smooth animation
                requestAnimationFrame(() => {
                    img.classList.add('loaded');
                    if (skeleton) {
                        skeleton.classList.add('hidden');
                    }
                });
            };

            img.onerror = () => {
                if (errorSrc) {
                    img.src = errorSrc;
                }
                if (skeleton) {
                    skeleton.classList.add('hidden');
                }
            };

            img.src = dataSrc;
            img.removeAttribute('data-src');
        }
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll('.lazy-image[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new LazyImageLoader();
    });
} else {
    new LazyImageLoader();
}

// jQuery fallback for existing code
$(document).ready(function () {
    // Handle images that don't use data-src (legacy support)
    $('.lazy-image:not([data-src])').each(function () {
        const img = $(this);
        const skeleton = img.siblings('.skeleton');

        img.on('load', function () {
            requestAnimationFrame(() => {
                img.addClass('loaded');
                skeleton.addClass('hidden');
            });
        });

        img.on('error', function () {
            const errorSrc = img.data('error');
            if (errorSrc) {
                img.attr('src', errorSrc);
            }
        });

        // Trigger load if src is already set
        if (img.attr('src')) {
            img.trigger('load');
        }
    });
});

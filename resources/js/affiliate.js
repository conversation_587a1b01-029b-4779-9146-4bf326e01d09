
window.addEventListener("load", async (event) => {

    const allowedKeys = [
        "a",
        "utm_source",
        "utm_medium",
        "utm_campaign",
        "utm_term",
        "utm_content",
        "pxl",
        "zoneid",
        "aff_id",
        "querystring",
    ];
    function storeQueryParamsInLocalStorage() {
        const affParams = new URLSearchParams(window.location.search);
        localStorage.removeItem("affParams"); 
        affParams.forEach((value, key) => {
            if (allowedKeys.includes(key)) {
                let storedParams = JSON.parse(localStorage.getItem("affParams")) || {};
                if (key === 'a' || key === 'aff_id') {
                    storedParams['aff_id'] = value;
                }
                storedParams[key] = value;
                localStorage.setItem("affParams", JSON.stringify(storedParams));
            }
        });
    }

    storeQueryParamsInLocalStorage();
});


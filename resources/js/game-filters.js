const GAME_MAP = {
    "quay-so-number-games":"lottery",
    "keno":"keno",
    "no-hu":"nohu",
    "game-bai":"game_cards",
    "xo-so":"xo-so",
    "ban-ca":"fishing",
    "quay-slots":"slots",
    "table-games":"tables",
    "game-nhanh":"instant",
    "game-khac":"other",

    //casino
    "xoc-dia":"xocdia",
    "rong-ho":"dragontiger",
    "tai-xiu":"sicbo"
}

const initPageState = ({ games, activeFilter }) => {
    if (games.length < activeFilter.limit) {
        $("#loadmore").toggle(false);
    }
    $("#searchKeyword").val(activeFilter.keyword);

    $("#clearKeyword").toggle(!!activeFilter.keyword);
};

const clearActiveStates = ({ selector, removeClassName, addClassName }) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((element) => {
        element.classList.remove(removeClassName);
        element.classList.add(addClassName);
    });
};

const setActiveState = ({ element, removeClassName, addClassName }) => {
    if (element) {
        element.classList.add(addClassName);
        element.classList.remove(removeClassName);
    }
};

const handlePageEvent = (params) => {
    const { activeFilter, games, onCreateElement, onCheckLiveStreamCasino } = params;
    activeFilter.page = 1;

    $("#searchKeyword").on(
        "input",
        debounce((e) => {
            const keyword = e.target.value;
            $("#clearKeyword").toggle(!!keyword);

            pushState({
                keyword: keyword.slice(0, 20),
            });
        }),
    );

    $("#clearKeyword").on("click", () => {
        $("#clearKeyword").toggle(false);
        pushState({
            keyword: "",
        });
    });

    $(".js-clear-search-game").on("click", () => {
        pushState({
            keyword: "",
        });
    });

    $(".filter-btn").on("click", (e) => {
        $('.filter-btn').removeClass('filter-active');
        $(e.target).addClass('filter-active');
        const queryParams = getQueryParamsFromSearchParams();
        activeFilter.page = 1;
        const pathname = window.location.pathname;
        let type = pathname.split("/").pop();
        if (
            ["song-bai-livecasino-truc-tuyen", "all", "cong-game"].includes(
                type
            )
        ) {
            type = "";
        }
        pushState({
            ...queryParams,
            filter: e.target.value ?? e.currentTarget.value,
            type,
        });
    });

    $(".js-provider-btn").on("click", (e) => {
        e.stopPropagation();
        e.preventDefault();
        $('.js-provider-btn .provider-btn').removeClass('provider-active');
        $(e.target).addClass('provider-active');
        activeFilter.page = 1;
        const queryParams = getQueryParamsFromSearchParams();
        pushState({
            ...queryParams,
            p: e.target.value ?? e.currentTarget.value,
        });
    });

    $(".type-btn").on("click", async (e) => {
        activeFilter.page = 1;
        pushState({
            type: e.target.value,
            keyword: ''
        });
    });

    $("#loadmore").on("click", function () {
        activeFilter.page++;
        const queryParams = getQueryParamsFromSearchParams();
        handleFetchGames({
            ...activeFilter,
            ...queryParams,
        });
    });

    initPageState({ games, activeFilter });

    const fetchGamesDebounced = debounce((queryParams = {}) =>
        handleFetchGames(queryParams),
    );

    function emptyContentReset (isRecent) {
        $('.js-keyword-empty').addClass('hidden');
        $('.js-keyword-empty').text('');
        if (isRecent) {
            $('.js-empty-button').removeClass('hidden');
            $('.js-empty-button .button-empty').text('Chơi ngay');
        } else {
            $('.js-empty-button').addClass('hidden');
        }
        $('.js-keyword-empty').text('Danh sách trò chơi đang cập nhật.');
        $('.js-empty-image').addClass('!hidden').addClass('hidden').removeClass('!block');
    }

    function searchEmpty () {
        $('.js-empty-image-search').removeClass('!hidden').removeClass('hidden').addClass('!block');
        $('.js-empty-title').text('Không tìm thấy kết quả.')
        $('.js-keyword-empty').removeClass('hidden');
        $('.js-keyword-empty').text(` "${$('#searchKeyword').val()}"`);
    }

    function favoriteEmpty () {
        $('.js-empty-image-favorite').removeClass('!hidden').removeClass('hidden').addClass('!block');
        $('.js-empty-title').text('Bạn chưa thêm game yêu thích nào.')
    }

    function filterEmpty (isRecent) {
        $('.js-empty-image-game').removeClass('!hidden').removeClass('hidden').addClass('!block');
        $('.js-empty-title').text(
            isRecent ? 'Bạn chưa chơi game nào gần đây.'
            : 'Danh sách trò chơi đang cập nhật.'
        );
        if (isRecent) {
            $('.js-empty-button').removeClass('hidden');
            $('.js-empty-button .button-empty').text('Chơi ngay');
        } else {
            $('.js-empty-button').addClass('hidden');
        }
    }

    const handleFetchGames = async (queryParams = {}) => {
        const rightFilter = $('.js-right-filter');
        try {
            if (queryParams.page === 1) {
                $(".js-games-loading").removeClass("hidden").addClass("flex");
                // $(".js-game-container-list").addClass('xl:!h-[1348px]');
            }
            $("#loadmore").prop("disabled", true).addClass("is-loading");
            const { page, type } = getSlugsFromUrl();

            if (!queryParams.keyword && (!queryParams.p || queryParams.p === "all") && (queryParams.type === 'favorite' || queryParams.sort === 'favorite') && queryParams.page === 1) {
                window.location.reload();
                return;
            }
            const endpoinSearch = {
                "cong-game": "/game",
                "song-bai-livecasino-truc-tuyen": '/casino'
            }
            const gameType = GAME_MAP[type] ?? type

            const data = await fetchData(
                `${endpoinSearch[page]}/search`,
                {
                    keyword: queryParams.keyword,
                    page: queryParams.page,
                    limit: queryParams.limit,
                    sort: type === 'favorite' ? 'favorite' : (!queryParams.filter ? 'hot' : queryParams.filter),
                    p: queryParams.p === "all" ? "" : queryParams.p,
                    type: gameType === "" ? "all" : (gameType === 'favorite' ? '' : gameType),
                },
                { useProxy: true },
                ''
            );

            // Clear current card
            if (queryParams.page === 1) {
                $("#game-container").empty();
            }
            const { items, page: currentPage, totalPage, total } = data.data;
            const urlParams = new URLSearchParams(window.location.search);
            const isRecent = !!urlParams.get('filter') && urlParams.get('filter') === 'recent';
            const isFilterProvider = !!urlParams.get('p') && urlParams.get('p') !== 'all';
            if (!isRecent) {
                $('.provider-dropdown').removeClass('!hidden');
                $('.filter-search').removeClass('!hidden');
                $('#search-button').removeClass('!hidden');
            }

            items.forEach((game) => {
                const ele = onCreateElement(game);
                $("#game-container").append(ele);
                setTimeout(() => {
                    onCheckLiveStreamCasino(game);
                }, 500)
            });
            addLabel();
            $('.js-current-game-count').text($("#game-container").children().length);
            $('.js-game-total').text(total);
            // update jackpot value from websocket
            updateJackpotValue();
            const loadmoreDisplay = currentPage < totalPage;
            if (loadmoreDisplay) {
                $('.js-games-loadmore').removeClass('hidden');
            } else {
                $('.js-games-loadmore').addClass('hidden');
            }
            $("#loadmore").prop("disabled", false).removeClass("is-loading").toggle(loadmoreDisplay);

            if (items.length === 0) {
                $('.js-empty-games').removeClass('hidden');
                $('#game-container').addClass('hidden');
                $('.js-games-loadmore').addClass('hidden');

                const isFavorite = window.location.pathname.includes('/favorite');
                const isFilterGame = !!urlParams.get('filter') || !!urlParams.get('p');
                const isSearchGame = !!urlParams.get('keyword');
                if (isRecent && !isFilterProvider && !isSearchGame) {
                    $('.provider-dropdown').addClass('!hidden');
                    $('.filter-search').addClass('!hidden');
                    $('#search-button').addClass('!hidden');
                } else {
                    $('.provider-dropdown').removeClass('!hidden');
                    $('.filter-search').removeClass('!hidden');
                    $('#search-button').removeClass('!hidden');
                }
                emptyContentReset(isRecent);
                if (isFavorite && !isSearchGame && !isFilterGame) {
                    rightFilter.addClass('!hidden');
                    favoriteEmpty();
                } else if (isSearchGame) {
                    searchEmpty();
                } else {
                    filterEmpty(isRecent);
                }
            } else {
                rightFilter.removeClass('!hidden');
                $('.js-empty-games').addClass('hidden');
                $('#game-container').removeClass('hidden');
            }
            addFavoriteGame();
        } catch (error) {
            console.log(error.message);
        } finally {
            setTimeout(() => {
                $(".js-games-loading").addClass("hidden").removeClass("flex");
                // $(".js-game-container-list").removeClass('xl:!h-[1348px]');
            }, 500);
        }
    };

    const updatedFilterUI = (params) => {
        const { keyword, p } = params;

        ["filter", "type"].forEach((key) => {
            const value = params[key];
            clearActiveStates({
                selector: `#${key}-buttons .${key}-btn`,
                removeClassName: "bg-active",
                addClassName: "bg-inactive",
            });

            const activeElement = document.querySelector(
                `#${key}-buttons .${key}-btn[value="${value}"]`,
            );

            setActiveState({
                element: activeElement,
                removeClassName: "bg-inactive",
                addClassName: "bg-active",
            });
        });

        // Update state of provider
        clearActiveStates({
            selector: "#provider-buttons .provider-btn",
            removeClassName: "border-active",
            addClassName: "border-inactive",
        });
        const activeElementProvider = document.querySelector(
            `#provider-buttons .provider-btn[value="${p}"]`,
        );
        setActiveState({
            element: activeElementProvider,
            removeClassName: "border-inactive",
            addClassName: "border-active",
        });

        // Update search keyword
        $("#searchKeyword").val(keyword);
        $("#clearKeyword").toggle(!!keyword);
    };

    window.addEventListener("pushstate", function (event) {
        activeFilter.page = 1;
        updatedFilterUI({
            filter: event.detail.state?.filter,
            keyword: event.detail.state?.keyword,
            p: event.detail.state?.p,
        });
        const queryParams = {
            ...activeFilter,
            ...event.detail.state,
        };
        fetchGamesDebounced(queryParams);
    });

    window.addEventListener("popstate", function (event) {
        activeFilter.page = 1;
        updatedFilterUI({
            filter: event.state?.filter || "",
            keyword: event.state?.keyword || "",
            p: event.state?.p || "",
        });
        const queryParams = {
            ...activeFilter,
            ...event.state,
        };
        fetchGamesDebounced(queryParams);
    });
};

window.handlePageEvent = handlePageEvent;

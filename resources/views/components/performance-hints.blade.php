{{-- Performance optimization hints and preloads --}}

{{-- Critical CSS and JS preloads --}}
<link rel="preload" href="{{ asset('build/assets/app-CrTTsayB.css') }}" as="style">
<link rel="preload" href="{{ asset('build/assets/vendor-ui-CSbci_z6.js') }}" as="script">

{{-- Font preloads for critical fonts --}}
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">
<link rel="preload" href="{{ asset('asset/fonts/icomoon/fonts/icomoon.woff2') }}" as="font" type="font/woff2" crossorigin>

{{-- Critical image preloads --}}
@isset($preLoadImg)
    <link rel="preload" href="{{ $preLoadImg }}" as="image">
@endisset

{{-- Hero banner critical images --}}
@if(request()->is('/'))
    <link rel="preload" href="{{ asset('asset/images/home/<USER>') }}" as="image" media="(min-width: 1200px)">
    <link rel="preload" href="{{ asset('asset/images/home/<USER>') }}" as="image" media="(max-width: 1199px)">
@endif

{{-- DNS prefetch for external domains --}}
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.bunny.net">
<link rel="dns-prefetch" href="//www.google.com">
<link rel="dns-prefetch" href="//code.jquery.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//cdn.socket.io">
<link rel="dns-prefetch" href="//challenges.cloudflare.com">

{{-- Preconnect to critical third-party origins --}}
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://fonts.bunny.net" crossorigin>
<link rel="preconnect" href="https://www.google.com" crossorigin>

{{-- Module preloads for better loading --}}
<link rel="modulepreload" href="{{ asset('build/assets/app-BnSYyFlm.js') }}">
<link rel="modulepreload" href="{{ asset('build/assets/lazy-image-bVuSW6Gx.js') }}">

{{-- Prefetch for likely next navigation --}}
@if(request()->is('/'))
    <link rel="prefetch" href="{{ asset('build/assets/sports-DTef5jHe.js') }}">
    <link rel="prefetch" href="{{ asset('build/assets/modal-DB3lvT2D.js') }}">
@endif

@if(request()->is('games*'))
    <link rel="prefetch" href="{{ asset('build/assets/game-filters-C52KY_8l.js') }}">
@endif

@if(request()->is('account*'))
    <link rel="prefetch" href="{{ asset('build/assets/index-Dg1Lm2pU.js') }}">
@endif

{{-- Preload critical API endpoints --}}
@auth
    <link rel="preload" href="/api/user/balance" as="fetch" crossorigin>
    <link rel="preload" href="/api/notifications" as="fetch" crossorigin>
@endauth

{{-- Resource hints for better performance --}}
<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width, Save-Data">
<meta name="color-scheme" content="dark light">

{{-- Early hints for critical resources --}}
<script>
    // Early resource loading
    if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
            // Preload non-critical resources during idle time
            const resources = [
                '{{ asset('build/assets/notification-Dbvkfb1l.js') }}',
                '{{ asset('build/assets/socket-CbC4QoCY.js') }}'
            ];
            
            resources.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = url;
                document.head.appendChild(link);
            });
        });
    }
    
    // Preload images on hover
    document.addEventListener('mouseover', function(e) {
        if (e.target.tagName === 'A' || e.target.closest('a')) {
            const link = e.target.closest('a');
            if (link && !link.dataset.preloaded) {
                link.dataset.preloaded = 'true';
                // Preload likely next page resources
                const prefetchLink = document.createElement('link');
                prefetchLink.rel = 'prefetch';
                prefetchLink.href = link.href;
                document.head.appendChild(prefetchLink);
            }
        }
    });
    
    // Intersection Observer for lazy loading optimization
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });
        
        // Observe all lazy images
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        });
    }
</script>

{{-- Critical CSS for immediate rendering --}}
<style>
    /* Critical above-the-fold styles */
    body { 
        font-family: Inter, sans-serif; 
        background-color: #13171C; 
        color: #FFFFFF; 
        margin: 0; 
        padding: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .container { 
        max-width: 1260px; 
        margin: 0 auto; 
        padding: 0 1rem; 
    }
    
    .hero-banner { 
        aspect-ratio: 370/110; 
        overflow: hidden; 
    }
    
    .hero-banner img { 
        width: 100%; 
        height: 100%; 
        object-fit: cover; 
        display: block;
    }
    
    /* Loading states */
    .loading { 
        opacity: 0.6; 
        pointer-events: none; 
    }
    
    /* Skeleton loading */
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Critical responsive utilities */
    @media (max-width: 1198px) {
        .xl\:hidden { display: none; }
        .container { padding: 0 0.5rem; }
    }
    
    @media (min-width: 1199px) {
        .max-xl\:hidden { display: none; }
    }
</style>

@props([
    'class' => '',
    'hiddenBottomBar' => false,
    'preLoadImg' => '',
])

@php
    $brandName = config('app.brand_name');
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">

    {{-- Optimized cache headers for better performance --}}
    <meta http-equiv="Cache-Control" content="public, max-age=3600">
    <meta name="format-detection" content="telephone=no">

    {{-- Performance hints --}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, interactive-widget=resizes-content">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link rel="dns-prefetch" href="//www.google.com">
    <link rel="dns-prefetch" href="//code.jquery.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.bunny.net" crossorigin>

    @isset($preLoadImg)
        <link as='image' rel="preload" href="{{ $preLoadImg }}">
    @endisset

    <!-- Enhanced SEO Meta Tags -->
    @isset($seo)
        @if ($seo)
            <x-seo-meta
                :title="$seo->title"
                :description="$seo->description ?? ''"
                :keywords="$seo->keywords ?? ''"
                :schemas="$seo->schemas ?? []"
            />
            @if (isset($seo->heading))
                @push('seo-heading')
                    <h1 class="sr-only">{{ $seo->heading }}</h1>
                @endpush
            @endif
        @endif
    @else
        <x-seo-meta
            :title="$brandName"
            :description="$brandName . ' - Premium gaming and entertainment platform'"
        />
    @endisset

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name='dmca-site-verification' content='L2l4cnZOcGhqeU81VDFkR3d2Y2l0QT090' />
    <meta name="google-site-verification" content="{{ env('GOOGLE_SITE_VERIFICATION') }}" />

    {{-- Optimized font loading --}}
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap"></noscript>

    <link rel="preload" href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap"></noscript>

    <link rel="preload" href="{{ asset('asset/fonts/icomoon/style.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="{{ asset('asset/fonts/icomoon/style.css') }}"></noscript>
    
    @include('components.global-vars-script')

    {{-- Performance optimization hints --}}
    @include('components.performance-hints')

    {{-- Critical CSS for above-the-fold content --}}
    @include('components.critical-css')

    {{-- Icon and PWA manifest --}}
    <link rel="shortcut icon" href="{{ asset('favicon.svg') }}" type="image/x-icon">
    <link rel="manifest" href="{{ asset('manifest.json') }}">
    <meta name="theme-color" content="#05E093">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Z22VIVU">
    <link rel="apple-touch-icon" href="{{ asset('favicon.svg') }}">

    {{-- Service Worker Registration --}}
    <script>
        // Register Service Worker for caching and offline support
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('ServiceWorker registration failed: ', error);
                    });
            });
        }
    </script>

    <!-- CF turnstile -->
    {{-- <script src="https://challenges.cloudflare.com/turnstile/v0/api.js"></script> --}}
    <!-- Google Captcha - Optimized loading -->
    <script>
        // Load reCAPTCHA asynchronously
        window.addEventListener('load', function() {
            const script = document.createElement('script');
            script.src = 'https://www.google.com/recaptcha/api.js?render={{config('google.recaptcha.site_key')}}';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        });
    </script>
    <!-- Styles -->
    @vite([
        'resources/sass/app.scss',
        'resources/js/app.js',
        'resources/js/popup.js',
        'resources/js/vip.js',
    ])

    @stack('sass')

    @stack('preloadLink')
    @if (env('GTM_ID'))
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '{{ env('GTM_ID') }}');
        </script>
    @endif
</head>

<x-header></x-header>

<body @class(['font-sans antialiased bg-neutral-790', $class])>
    @hasSection('account-header')
        @yield('account-header')
    @endif
    <div @class([
        'container' => in_array(request()->path(), config('execeptContainer', [])),
        'bg-neutral-790 min-h-[calc(100vh_-_110px)] xl:min-h-[calc(100vh_-_756px)]',
    ])>
        {{-- SEO Heading --}}
        @stack('seo-heading')

        {{ $slot }}
    </div>

    @if (!$hiddenBottomBar)
        <x-ui.bottom-bar></x-ui.bottom-bar>
    @endif
    <x-ui.minigame.index />
    @hasSection('beforeBodyClose')
        @yield('beforeBodyClose')
    @endif

    <div id="footer-content" class="max-xl:hidden">
        <x-footer />
    </div>
    <x-kit.floating-button />

    <script>
        window.notiModal = `<x-ui.modal modalClass="bg-neutral-700" :id="'noti-modal'">
                        <x-ui.popup.notify></x-ui.popup.notify>
                    </x-ui.modal>`;

        window.labelNew = `<x-kit.label type="new" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelEvent = `<x-kit.label type="event" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelHot = `<x-kit.label type="hot" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelLive = `<x-kit.label type="live" direction="horizontal" size="custom"></x-kit.label>`;
    </script>
    {{-- Optimized script loading --}}
    <script>
        // Load jQuery and dependencies asynchronously
        window.addEventListener('load', function() {
            // Load jQuery
            const jqueryScript = document.createElement('script');
            jqueryScript.src = 'https://code.jquery.com/jquery-3.7.1.min.js';
            jqueryScript.integrity = 'sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=';
            jqueryScript.crossOrigin = 'anonymous';
            jqueryScript.onload = function() {
                // Load jQuery Validate after jQuery is loaded
                const validateScript = document.createElement('script');
                validateScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js';
                validateScript.crossOrigin = 'anonymous';
                validateScript.onload = function() {
                    // Load jQuery Validate Unobtrusive after jQuery Validate is loaded
                    const unobtrusiveScript = document.createElement('script');
                    unobtrusiveScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js';
                    unobtrusiveScript.crossOrigin = 'anonymous';
                    document.head.appendChild(unobtrusiveScript);
                };
                document.head.appendChild(validateScript);
            };
            document.head.appendChild(jqueryScript);

            // Load Socket.IO
            const socketScript = document.createElement('script');
            socketScript.src = 'https://cdn.socket.io/4.8.1/socket.io.esm.min.js';
            socketScript.type = 'module';
            socketScript.defer = true;
            document.head.appendChild(socketScript);

            // Load nanoplayer
            const nanoScript = document.createElement('script');
            nanoScript.src = '{{ asset('js/nanoplayer.4.min.js') }}';
            document.head.appendChild(nanoScript);
        });
    </script>
    @stack('scripts')
    <x-livechat />
    @if (env('GTM_ID'))
    <noscript><iframe
        src="https://www.googletagmanager.com/ns.html?id={{ env('GTM_ID') }}"
        height="0" width="0"
        style="display:none;visibility:hidden"></iframe>
    </noscript>
@endif
<go-jackpot pos="auto 10px 10% auto" class="z-[50]"></go-jackpot>
<script type="module"  src="{{ asset('asset/js/float-go-icon.js') }}"></script>

{{-- Event Fifa Club --}}
<script src="{{ Module::asset('rewardgoldenhour:js/app.js') }}"></script>
</body>
</html>

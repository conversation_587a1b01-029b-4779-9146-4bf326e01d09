@props([
    'class' => '',
    'hiddenBottomBar' => false,
    'preLoadImg' => '',
])

@php
    $brandName = config('app.brand_name');
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">

    {{-- No cache, ensure the page is always up to date --}}
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="format-detection" content="telephone=no">

    @isset($preLoadImg)
        <link as='image' rel="preload" href="{{ $preLoadImg }}">
    @endisset

    <!-- SEO -->
    @isset($seo)
        @if ($seo)
            <title>{{ $seo->title }}</title>
            <meta name="title" content="{{ $seo->title }}" />
            <meta name="description" content="{{ $seo->description ?? '' }}" />
            <meta name="keywords" content="{{ $seo->keywords ?? '' }}">
            <link rel="canonical" href="{{ url()->current() }}">
            <meta property="og:title" content="{{ $seo->title ?? '' }}">
            <meta property="og:description" content="{{ $seo->description ?? '' }}">
            <meta property="og:type" content="website">
            <meta property="og:url" content="{{ url()->current() }}">
            <meta property="og:image" content="{{ asset('favicon.svg') }}" />
            <meta name="twitter:title" content="{{ $seo->title }}">
            <meta name="twitter:description" content="{{ $seo->description ?? '' }}">
            <meta name="twitter:image" content="{{ asset('favicon.svg') }}">
            @if (isset($seo->schemas))
                @foreach ($seo->schemas as $jsonLdItem)
                    <script type="application/ld+json">
                {!! $jsonLdItem !!}
                </script>
                @endforeach
            @endif
        @endif
    @else
        <title>{{ $brandName }}</title>
        <meta name="title" content="{{ $brandName }}" />
        <meta name="description" content="{{ $brandName }}" />

    @endisset

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name='dmca-site-verification' content='L2l4cnZOcGhqeU81VDFkR3d2Y2l0QT090' />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, interactive-widget=resizes-content">
    <meta name="google-site-verification" content="{{ env('GOOGLE_SITE_VERIFICATION') }}" />
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&amp;display=swap">
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('asset/fonts/icomoon/style.css') }}">
    
    @include('components.global-vars-script')

    {{-- Icon --}}
    <link rel="shortcut icon" href="{{ asset('favicon.svg') }}" type="image/x-icon">

    <!-- CF turnstile -->
    {{-- <script src="https://challenges.cloudflare.com/turnstile/v0/api.js"></script> --}}
    <!-- Google Captcha -->
    <script async src="https://www.google.com/recaptcha/api.js?render={{config('google.recaptcha.site_key')}}" defer></script>
    <!-- Styles -->
    @vite([
        'resources/sass/app.scss',
        'resources/js/app.js',
        'resources/js/popup.js',
        'resources/js/vip.js',
    ])

    @stack('sass')

    @stack('preloadLink')
    @if (env('GTM_ID'))
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '{{ env('GTM_ID') }}');
        </script>
    @endif
</head>

<x-header></x-header>

<body @class(['font-sans antialiased bg-neutral-790', $class])>
    @hasSection('account-header')
        @yield('account-header')
    @endif
    <div @class([
        'container' => in_array(request()->path(), config('execeptContainer', [])),
        'bg-neutral-790 min-h-[calc(100vh_-_110px)] xl:min-h-[calc(100vh_-_756px)]',
    ])>
        {{-- SEO --}}
        @if (isset($seo->heading))
            <h1 class="hidden">{{ $seo->heading }}</h1>
        @endif

        {{ $slot }}
    </div>

    @if (!$hiddenBottomBar)
        <x-ui.bottom-bar></x-ui.bottom-bar>
    @endif
    <x-ui.minigame.index />
    @hasSection('beforeBodyClose')
        @yield('beforeBodyClose')
    @endif

    <div id="footer-content" class="max-xl:hidden">
        <x-footer />
    </div>
    <x-kit.floating-button />

    <script>
        window.notiModal = `<x-ui.modal modalClass="bg-neutral-700" :id="'noti-modal'">
                        <x-ui.popup.notify></x-ui.popup.notify>
                    </x-ui.modal>`;

        window.labelNew = `<x-kit.label type="new" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelEvent = `<x-kit.label type="event" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelHot = `<x-kit.label type="hot" direction="horizontal" size="custom"></x-kit.label>`;
        window.labelLive = `<x-kit.label type="live" direction="horizontal" size="custom"></x-kit.label>`;
    </script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="
        crossorigin="anonymous"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"
        crossorigin="anonymous"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js"
        crossorigin="anonymous"></script>
    <script type="module" defer src="https://cdn.socket.io/4.8.1/socket.io.esm.min.js"></script>
    <script src="{{ asset('js/nanoplayer.4.min.js') }}"></script>
    @stack('scripts')
    <x-livechat />
    @if (env('GTM_ID'))
    <noscript><iframe
        src="https://www.googletagmanager.com/ns.html?id={{ env('GTM_ID') }}"
        height="0" width="0"
        style="display:none;visibility:hidden"></iframe>
    </noscript>
@endif
<go-jackpot pos="auto 10px 10% auto" class="z-[50]"></go-jackpot>
<script type="module"  src="{{ asset('asset/js/float-go-icon.js') }}"></script>

{{-- Event Fifa Club --}}
<script src="{{ Module::asset('rewardgoldenhour:js/app.js') }}"></script>
</body>
</html>

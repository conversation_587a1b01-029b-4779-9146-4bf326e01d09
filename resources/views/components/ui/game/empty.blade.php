@php
    $isFavorite = request()->route('slug') === 'favorite';
    $isFavoriteEmpty = $isFavorite &&
    !request()->filter &&
    !request()->keyword &&
    !request()->p;
    $link = str_starts_with(request()->getPathInfo(), '/song-bai-livecasino-truc-tuyen/')
        ? route('en.casino.index')
        : route('en.games.index');
    $isFilter = (request()->filter ?? null || request()->p ?? null) && !request()->keyword;
    $isSearch = isset(request()->keyword) && request()->keyword;
    $isRecent = request()->filter === 'recent';
@endphp

<div @class([
    'flex flex-col items-center justify-center h-full min-h-[368px] text-sm xl:text-xs text-neutral-200 xl:min-h-[400px]',
])>
    <img
        src="{{ asset('asset/images/games/empty-favorite.svg') }}" alt="empty"
        @class([
            "js-empty-image js-empty-image-favorite h-[70px] w-[70px]",
            "hidden" => $isSearch || $isFilter,
        ])
    >
    <img
        src="{{ asset('asset/images/games/ic-game-empty-search.webp') }}" alt="empty"
        @class([
            "js-empty-image js-empty-image-search h-[70px] w-[70px]",
            "hidden" => !$isSearch,
        ])
    >
    <img
        src="{{ asset('asset/images/games/empty-game.svg') }}" alt="empty"
        @class([
            "js-empty-image js-empty-image-game h-[70px] w-[70px] hidden",
            "!block" => $isFilter && !$isRecent,
        ])
    >

    <img
        src="{{ asset('asset/images/games/empty-game.svg') }}" alt="empty"
        @class([
            "js-empty-image js-empty-image-recent h-[70px] w-[70px] hidden",
            "!block" => $isFilter && $isRecent,
        ])
    >
    
    <div class="mt-4 text-xs">
        <span class="js-empty-title">
            @if ($isFavoriteEmpty)
                <div class="max-xl:max-w-[250px] text-center">
                    Bạn chưa thêm game yêu thích nào. <br />
                    Vui lòng thêm game thông qua biểu tượng <i class="icon-unfavorite"></i> trên thẻ game.
                </div>
            @elseif ($isSearch)
                Không tìm thấy kết quả.
            @elseif ($isRecent)
                Bạn chưa chơi game nào gần đây.
            @else
                Danh sách trò chơi đang cập nhật.
            @endif
        </span>
        <span class="!hidden js-keyword-empty {{ request()->keyword ? '' : 'hidden' }}">
            "{{ isset(request()->keyword) && request()->keyword ? substr(request()->keyword, 0, 20) : '' }}"
        </span>
    </div>
    <div @class([
        "js-empty-button",
        "hidden" => !$isFavoriteEmpty && !$isRecent,
    ])>
        <x-kit.button
            :link="$link"
            type="gradient-secondary"
            class="button-empty mt-6 w-[127px] truncate px-0 capitalize"
            size="large"
        >
            {{
                $isRecent ? 'Chơi Ngay' : 'Thêm tại đây'
            }}
        </x-kit.button>
    </div>
</div>

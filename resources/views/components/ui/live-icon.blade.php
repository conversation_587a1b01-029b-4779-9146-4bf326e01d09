@props([
    'gap' => 10,
    'dotSize' => 10,
    'dotInnerSize' => 6,
    'textSize' => 12,
    'letterSpacing' => 0.5,
    'showLiveText' => false,
    'liveTextClass' => '',
])

<div 
    class="live-indicator"
    style="--live-gap: {{ $gap }}px; --live-dot-size: {{ $dotSize }}px; --live-dot-inner-size: {{ $dotInnerSize }}px; --live-text-size: {{ $textSize }}px; --live-letter-spacing: {{ $letterSpacing }}px;"
>
    <span class="live-dot">
        <span class="wave"></span>
        <span class="wave"></span>
    </span>
    @if ($showLiveText)
        <span class="live-text {{ $liveTextClass }}">
            <span>L</span><span>I</span><span>V</span><span>E</span>
        </span>
    @endif
</div>

{{-- Example for custom style --}}
{{-- <div class="flex justify-center items-center w-full h-full">
    <x-ui.live-icon gap="12" dotSize="32" dotInnerSize="18" textSize="32" letterSpacing="1" />
</div> --}}

@props(['title', 'games', 'activeFilter', 'filters', 'gamesTotal', 'routeUrl', 'swiperConfig', 'type' => 'game', 'streamGames' => []])

<div class="js-game-container-list relative min-h-[440px] w-full pb-4 xl:mb-[80px] xl:min-h-[444px] xl:pb-0">
    {{-- <!-- <div>
        <x-ui.providers-filter :providers="$filters['typeOptions']" :$activeFilter id="provider-buttons" />
    </div> --> --}}
    @if (request()->route('slug') !== 'da-ga')
        <div>
            <x-ui.types-filter :types="$filters['typeOptions'] ?? []" :$activeFilter id="type-buttons" :$swiperConfig />
        </div>
    @endif
    @if (request()->route('slug') !== 'da-ga')
        <div class="mb-5 mt-2 flex flex-wrap items-center justify-between gap-y-2 xl:mb-8 xl:mt-6 xl:flex-row">

            <div class="hidden flex-wrap items-center gap-2 xl:flex">
                @if (request()->route('slug') !== 'favorite')
                    <x-ui.filter :filters="$filters['sortOptions']" :$activeFilter id="filter-buttons" />
                @endif
            </div>

            <div class="relative flex w-full items-center justify-between gap-2 xl:flex xl:w-auto">
                <div class="flex w-full items-center gap-2">
                    @php
                        $allOption = (object) ['label' => 'Nhà Cung Cấp', 'value' => 'all', 'key' => 'all', 'icon' => 'all'];
                        $providerOptions = array_merge([$allOption], $filters['providerOptions'] ?? []);
                        $defaultProviderValue = null;
                        if (!request()->get(key: 'p')) {
                            $defaultProviderValue = $providerOptions[0];
                        } else {
                            foreach ($providerOptions as $provider) {
                                if ($provider->value === request()->get(key: 'p')) {
                                    $defaultProviderValue = $provider;
                                    break;
                                }
                            }
                        }
                        if (isset($defaultProviderValue) && $defaultProviderValue->value === 'all') {
                            $defaultProviderValue->icon = 'all';
                        }
                        $defaultValue = $defaultProviderValue->value ?? '';
                    @endphp
                    <div class="provider-dropdown flex h-[40px] w-[calc(178/306*100%)] xl:w-[190px] xl:min-w-[190px]">
                        @if (isset($filters['providerOptions']) && count($filters['providerOptions']) > 1)
                            <x-kit.dropdown-new 
                                placeholder="Nhà cung cấp" 
                                itemClass="js-provider-btn text-neutral-200 h-11 [&.selected]:pointer-events-none" 
                                id="provider"
                                :options="$providerOptions" 
                                :value="$defaultValue" 
                                :defaultValue="$defaultProviderValue" 
                                class="provider-btn px-3 py-2" isHasIcon
                                classValue="!text-white" 
                                activeClass="provider-active [&.provider-active]:pointer-events-none" 
                                capitalizeOption="true" 
                            />
                        @endif
                    </div>
                    @if (isset($filters['sortOptions']) && count($filters['sortOptions']) > 0)
                        <div class="filter-dropdown flex h-10 flex-1 xl:hidden [&_.dropdown-button]:capitalize">
                            @php
                                $sortDefault = isset($filters['sortOptions'][1]) ? $filters['sortOptions'][1] : null;
                                if (request()->get(key: 'filter') && $sortDefault) {
                                    foreach ($filters['sortOptions'] as $sort) {
                                        if ($sort['value'] === request()->get(key: 'filter')) {
                                            $sortDefault = $sort;
                                            break;
                                        }
                                    }
                                }
                            @endphp
                            @if (request()->route('slug') !== 'favorite')
                                <x-kit.dropdown-new placeholderIcon="" id="filter" :options="$filters['sortOptions']" :defaultValue="$sortDefault"
                                    activeClass="filter-active" class="filter-btn sort-game-item capitalize" />
                            @endif
                        </div>
                    @endif
                </div>
                @if (!empty($games))
                    <button id="search-button"
                        class="flex h-10 w-[44px] min-w-[44px] items-center justify-center rounded-[8px] border border-neutral-650 bg-neutral-700 xl:hidden">
                        <img src="{{ asset('asset/icons/ic-search.svg') }}" class="search-icon h-[20px] w-[20px]" alt="icon" />
                        <img src="{{ asset('asset/icons/ic-close.svg') }}" class="close-icon hidden h-[24px] w-[24px]" alt="icon" />
                    </button>
                @endif
                <div class="filter-search mobile-search hidden shrink-0 xl:block xl:w-[200px]">
                    <x-kit.search-input id="searchKeyword" class="!h-[40px] border md:w-full" placeholder="{{ __('common.search') }}"
                        value="{{ isset(request()->keyword) && request()->keyword ? substr(request()->keyword, 0, 20) : '' }}"
                        showClose></x-kit.search-input>
                </div>
            </div>
        </div>
    @endif
    {{-- @if ($type === 'casino')
        <div class="xl:hidden">
            <x-ui.home.live type="lobby" :gameList="$streamGames" />
        </div>
    @endif --}}
    <div id="game-container"
        class="game-container {{ empty($games) ? 'hidden' : '' }} relative z-[2] grid grid-cols-2 gap-x-3 gap-y-3 pb-[20px] md:grid-cols-3 xl:grid-cols-6 xl:gap-x-5 xl:gap-y-5 xl:pb-8">
        @foreach ($games as $game)
            <x-ui.card 
                :game="$game" name="{{ $game->name ?? 'title' }}" 
                image="{{ $game->image ?? '' }}" 
                type="{{ $type }}"
                data-api="{{ $game->api_url ?? '' }}" 
                id="{{ $game->partner_game_id ?? '' }}"
                tableId="{{ $game->table_id ?? '' }}"
                jackpotValue="{{ $game->jackpot ?? 0 }}"
                favorite="{{ isset($game->is_favorite) && $game->is_favorite ? 'favorite' : '' }}"
                provider="{{ $game->partner_txt ?? '' }}" 
                partner="{{ $game->partner ?? '' }}" 
                tags="{{ $game->tags ?? '' }}"
                class="text-marron loader-image-transparent flex flex-col items-center"
                partner_provider="{{ $game->partner_provider ?? '' }}" 
                isFavoriteCenter
                cardLabelClass="max-xl:left-[8px]"
                jackpotWrapperClass="xl:py-[5px] py-[4px] xl:px-[9.5px] px-[7px] [&>div]:leading-[15px] gap-[4px] max-xl:rounded-bl-[8px] right-[-0.5px] max-xl:h-[23px] [&>div.js-jackpot-value]:max-xl:!text-[12px]"
            />
        @endforeach
    </div>

    <div class="js-games-loadmore {{ isset($gamesTotal) && count($games) < $gamesTotal ? '' : 'hidden' }} flex justify-center">
        <x-kit.button id="loadmore" style="filled" type="gradient-secondary" size="medium" class="flex h-10 min-w-[179px]">
            <span class="text-sm font-medium capitalize">
                xem thêm
                (<span class="js-current-game-count">{{ count($games) }}</span>/<span class="js-game-total">{{ $gamesTotal ?? count($games) }}</span>)
            </span>
        </x-kit.button>
    </div>

    <div class="js-empty-games {{ empty($games) ? '' : 'hidden' }}">
        <x-ui.game.empty />
    </div>
</div>

@pushOnce('scripts')
    @vite(['resources/js/game-filters.js'])

    <script>
        const filters = @json($filters);
        const games = @json($games);
        const activeFilter = @json($activeFilter);
        const type = @json($type);
        const casinoLiveStream = @json(config('constants.gameLiveStreamCasino'));
        const gameIdCasinoLiveStream = casinoLiveStream.map(item => item.gameId);
        window.addEventListener("load", (event) => {
            handleDropdown();
            const searchBtn = document.getElementById('search-button');
            const searchInput = document.querySelector('.mobile-search');
            const searchIcon = searchBtn?.querySelector('.search-icon');
            const closeIcon = searchBtn?.querySelector('.close-icon');

            // Add click event listener to document
            document.addEventListener('click', (event) => {
                const isClickInside = searchBtn?.contains(event.target) || searchInput?.contains(event
                    .target);

                if (!isClickInside && !searchInput?.classList?.contains('hidden')) {
                    closeSearch();
                }
            });

            // Function to close search
            const closeSearch = () => {
                searchInput?.classList?.add('hidden');
                searchInput?.classList?.add('xl:block');
                searchInput?.classList?.remove('show');
                searchIcon?.classList?.remove('hidden');
                closeIcon?.classList?.add('hidden');
            };

            searchBtn?.addEventListener('click', (event) => {
                event.preventDefault();
                // event.stopPropagation(); // Prevent document click from immediately closing
                searchInput?.classList?.toggle('hidden');
                searchInput?.classList?.toggle('xl:block');
                searchIcon?.classList?.toggle('hidden');
                closeIcon?.classList?.toggle('hidden');
                setTimeout(() => {
                    searchInput?.classList?.toggle('show');
                }, 10);
                if (!searchInput?.classList?.contains('hidden')) {
                    searchInput?.focus();
                }
            });

            const createElement = (game) => {
                const gameJson = encodeURIComponent(JSON.stringify(game));
                const gameFavorite = game.is_favorite ? 'favorite' : '';
                const gameTags = game.tags ? game.tags : '';

                const partners = ['rik', 'b52', 'go', '789club', 'sunwin', 'techplay'];
                const jackpotId = partners.includes(game.partner) 
                    ? `${game.partner}_${game.partner_game_id}`
                    : game.partner_game_id;

                if (gameIdCasinoLiveStream.includes(`${game.partner_provider}_${game.partner_game_id}`)) {
                    if (game.is_favorite) {
                        return `<x-ui.card
                        name="${game.name}"
                        image="${game.image}"
                        type="casino"
                        game="${JSON.stringify(game)}"
                        partner="${game.partner}"
                        data-game-id="${game.partner_game_id}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        tableId="${game.table_id ?? null}"
                        jackpotValue="${game.jackpot ?? 0}"
                        favorite="favorite"
                        partner_provider="${game.partner_provider}"
                        tags='${gameTags}'
                        provider="${game.partner_txt}"
                        js-jackpot-id="${jackpotId}"
                        data-maintain="${game.maintain ?? ''}"
                        isRenderLiveCasino
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
                    }
                    return `<x-ui.card
                            name="${game.name}"
                            image="${game.image}"
                            type="casino"
                            game="${JSON.stringify(game)}"
                            partner="${game.partner}"
                            partner_provider="${game.partner_provider}"
                            data-game-id="${game.partner_game_id}"
                            data-api="${game.api_url}"
                            id="${game.partner_game_id}"
                            tableId="${game.table_id ?? null}"
                            jackpotValue="${game.jackpot ?? 0}"
                            favorite="''"
                            provider="${game.partner_txt}"
                            js-jackpot-id="${jackpotId}"
                            tags="${gameTags}"
                            data-maintain="${game.maintain ?? ''}"
                            isRenderLiveCasino
                            class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
                }

                if (game.is_favorite) {
                    return `<x-ui.card
                    name="${game.name}"
                    image="${game.image}"
                    type="${type}"
                    game="${JSON.stringify(game)}"
                    partner="${game.partner}"
                    partner_provider="${game.partner_provider}"
                    data-game-id="${game.partner_game_id}"
                    data-api="${game.api_url}"
                    id="${game.partner_game_id}"
                    tableId="${game.table_id ?? null}"
                    jackpotValue="${game.jackpot ?? 0}"
                    favorite="favorite"
                    tags='${gameTags}'
                    provider="${game.partner_txt}"
                    js-jackpot-id="${jackpotId}"
                    data-maintain="${game.maintain ?? ''}"
                    class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
                }
                return `<x-ui.card
                        name="${game.name}"
                        image="${game.image}"
                        type="${type}"
                        game="${JSON.stringify(game)}"
                        partner="${game.partner}"
                        partner_provider="${game.partner_provider}"
                        data-game-id="${game.partner_game_id}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        tableId="${game.table_id ?? null}"
                        jackpotValue="${game.jackpot ?? 0}"
                        favorite="''"
                        provider="${game.partner_txt}"
                        tags="${gameTags}"
                        js-jackpot-id="${jackpotId}"
                        data-maintain="${game.maintain ?? ''}"
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
            };

            const onCheckLiveStreamCasino = (game) => {
                const liveCasinoData = casinoLiveStream.find(item => item.gameId === game.partner_provider + '_' + game
                    .partner_game_id);
                if (liveCasinoData) {
                    openLiveStream(liveCasinoData.gameId);
                }
            }

            handlePageEvent({
                games,
                activeFilter,
                onCreateElement: createElement,
                onCheckLiveStreamCasino: onCheckLiveStreamCasino,
            })
        });

        window.addEventListener('DOMContentLoaded', () => {
            const lobbyType = '{{ $type }}';
            checkLabel();
            const containerLiveStream = $('#game-container');
            containerLiveStream.find('.js-live-game-item').each(function(index, item) {
                const id = $(item).attr('id');
                openLiveStream(id);
            });
        })
    </script>
@endpushOnce

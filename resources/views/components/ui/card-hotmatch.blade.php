@props(['hotMatch'])
@use(App\Enums\GatewayEndpoint)
@use(App\Enums\UrlPathEnum)
<div
    @class([
        "rounded-lg cursor-pointer w-full bg-card-hot-match-bg xl:hover:bg-card-hot-match-stroke-hover transition-all duration-300 bg-100 relative xl:py-[18px] pb-1.5 xxs:pb-[11.5px] xl:pb-[18px] py-1 xxs:py-[10px] xl:px-[16px] px-[7.5px] xl:min-h-[170px] xl:min-w-[400px]",
        "before:absolute before:inset-[1px] before:bg-card-hot-match-bg before:bg-100 before:z-[0] before:rounded-lg"
    ])
    onclick="openSport({
        link: '{{ UrlPathEnum::K_SPORTS }}' + '?leagueId={{ $hotMatch->league_id }}&matchId={{ $hotMatch->match_id }}',
        apiUrl: '{{ GatewayEndpoint::K_SPORTS }}',
        loginRequired: '{{ $hotMatch->loginRequired ?? false }}',
        params: {
            leagueId: '{{ $hotMatch->league_id }}',
            matchId: '{{ $hotMatch->match_id }}',
        }
    })"
>
    <div
        @class([
            "flex gap-1 z-[1] items-center pl-[7px] absolute left-0 top-0 bg-[url('/resources/img/home/<USER>')] bg-cover h-[21px] w-[141px] rounded-tl-lg max-[360px]:w-[7rem] max-[385px]:w-[7.5rem] max-[385px]:[background-size:100%_100%]",
            "xl:bg-[url('/public/asset/images/home/<USER>/label-hotmatch.avif')] xl:h-7 xl:w-[187px] xl:pl-3"
        ])
    >
        <img src="{{ $hotMatch->league_image }}" alt="hotmatch" class="h-4 w-4 object-contain"
            loading="lazy"
            onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
        />
        <span
            class="text-xs text-center text-neutral truncate pr-6">{{ $hotMatch->league_name_text }}</span>
    </div>

    <div class="flex justify-between relative z-[1]">
        <div class="flex flex-col pt-[17px] sm:pt-[22px] xl:pt-[18px] gap-2">
            <div class="flex items-center gap-1.5 xl:gap-2 xl:py-[3px] py-[2.5px]">
                <img src="{{ $hotMatch->teams[0]->flag_thumbnail }}" alt="hotmatch"
                    class="h-[16px] w-[16px] xl:h-[24px] xl:w-[24px] object-contain" loading="lazy"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
                <span
                    class="text-sm text-center text-neutral truncate">{{ $hotMatch->teams[0]->name ?? '-' }}</span>
            </div>
            <div class="flex items-center gap-1.5 xl:gap-2 xl:py-[3px] py-[2.5px]">
                <img src="{{ $hotMatch->teams[1]->flag_thumbnail }}" alt="hotmatch"
                    class="h-[16px] w-[16px] xl:h-[24px] xl:w-[24px] object-contain" loading="lazy"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
                <span
                    class="text-sm text-neutral truncate">{{ $hotMatch->teams[1]->name ?? '-' }}</span>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-y-1.5 xl:gap-x-1.5 gap-x-[4.5px] min-w-[120px] xs:min-w-[150px] xl:min-w-[176px]">
            <span class="text-[10px] text-[#B7C2D8] text-center leading-[14px]">HDP</span>
            <span class="text-[10px] text-[#B7C2D8] text-center leading-[14px]">O/U</span>
            <div
                class="h-[32px] p-[3.5px] text-neutral xl:p-[5.5px] bg-[#31403F] rounded-[4px] xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-[72px] xl:w-[85px] leading-[14px] xl:leading-[18px]">
                <span class="w-1/2 text-center">{{ isset($hotMatch->hdp->hTeam->rate) ? $hotMatch->hdp->hTeam->rate : '-' }}</span>
                <span
                    @class([
                        "w-1/2 text-center",
                        "text-secondary-500" => isset($hotMatch->hdp->hTeam->odds) && floatval($hotMatch->hdp->hTeam->odds) < 0,
                        "text-primary-500" => !isset($hotMatch->hdp->hTeam->odds) || floatval($hotMatch->hdp->hTeam->odds) >= 0
                    ])
                >
                    {{ $hotMatch->hdp->hTeam->odds ?? '-' }}
                </span>
            </div>
            <div
                class="h-[32px] p-[3.5px] text-neutral xl:p-[5.5px] bg-[#31403F] rounded-[4px] xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-[72px] xl:w-[85px] leading-[14px] xl:leading-[18px]">
                <span class="w-1/2 text-center">{{ $hotMatch->ou->hTeam->rate ?? '-' }}</span>
                <span
                    @class([
                        "w-1/2 text-center",
                        "text-secondary-500" => isset($hotMatch->ou->hTeam->odds) && floatval($hotMatch->ou->hTeam->odds) < 0,
                        "text-primary-500" => !isset($hotMatch->ou->hTeam->odds) || floatval($hotMatch->ou->hTeam->odds) >= 0
                    ])
                >
                    {{ $hotMatch->ou->hTeam->odds ?? '-' }}
                </span>
            </div>
            <div
                class="h-[32px] p-[3.5px] text-neutral xl:p-[5.5px] bg-[#31403F] rounded-[4px] xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-[72px] xl:w-[85px] leading-[14px] xl:leading-[18px]">
                <span class="w-1/2 text-center">{{ $hotMatch->hdp->aTeam->rate ?? '-' }}</span>
                <span
                    @class([
                        "w-1/2 text-center",
                        "text-secondary-500" => isset($hotMatch->hdp->aTeam->odds) && floatval($hotMatch->hdp->aTeam->odds) < 0,
                        "text-primary-500" => !isset($hotMatch->hdp->aTeam->odds) || floatval($hotMatch->hdp->aTeam->odds) >= 0
                    ])
                >
                    {{ $hotMatch->hdp->aTeam->odds ?? '-' }}
                </span>
            </div>
            <div
                class="h-[32px] p-[3.5px] text-neutral xl:p-[5.5px] bg-[#31403F] rounded-[4px] xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-[72px] xl:w-[85px] leading-[14px] xl:leading-[18px]">
                <span class="w-1/2 text-center">{{ $hotMatch->ou->aTeam->rate ?? '-' }}</span>
                <span
                    @class([
                        "w-1/2 text-center",
                        "text-secondary-500" => isset($hotMatch->ou->aTeam->odds) && floatval($hotMatch->ou->aTeam->odds) < 0,
                        "text-primary-500" => !isset($hotMatch->ou->aTeam->odds) || floatval($hotMatch->ou->aTeam->odds) >= 0
                    ])
                >
                    {{ $hotMatch->ou->aTeam->odds ?? '-' }}
                </span>
            </div>
        </div>

    </div>
    <div class="flex justify-between items-center pt-[9px] xl:pt-4 relative z-[1]">
        <div class="flex gap-1 text-xs">
            <span class="text-neutral-850 xl:text-neutral-400 match-time-{{ $hotMatch->match_id }}"></span>
            <span class="text-neutral-600 xl:text-neutral-400 match-date-{{ $hotMatch->match_id }}"></span>
        </div>
        <x-kit.button
            class="[&_span]:text-[20px]"
            size="small"
            rightIcon='icon-effect-arrow'
            style="filled"
            type="gradient-secondary"
            class="hidden capitalize xl:flex xl:min-w-[176px]"
        >
            <span class="text-[12px] leading-[18px] font-medium whitespace-nowrap">Cược ngay</span>
        </x-kit.button>
    </div>
</div>

<script>
    (function() {
        const dateObj = new Date('{{ $hotMatch->text_time }}');

        const time = dateObj.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        const date_str = dateObj.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit'
        });
        const times = document.querySelectorAll('.match-time-{{ $hotMatch->match_id }}');
        const dates = document.querySelectorAll('.match-date-{{ $hotMatch->match_id }}');

        times.forEach(item => item.innerHTML = time);
        dates.forEach(item => item.innerHTML = date_str);
    })();
</script>

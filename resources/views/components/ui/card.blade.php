@props([
    'name',
    'image',
    'id',
    'tableId',
    'provider',
    'partner',
    'game' => null,
    'favorite' => '',
    'type' => 'game',
    'tags' => '',
    'page' => 'lobby',
    'partner_provider' => '',
    'isFavoriteCenter' => true,
    'isRenderLiveCasino' => false,
    'imgWrapClass' => '',
    'cardLabelClass' => '',
    'jackpotWrapperClass' => '',
    'isLiveGameSquare' => true,
    'jackpotClass' => '',
    'providerClass' => '',
    'jsJackpotId' => '',
    'nameClass' => '',
    'isMbResponsive' => false,
    'jackpotValue' => 0,
])

@php
    $gameLiveStreamCasino = collect(config('constants.gameLiveStreamCasino'));
    $isGameLiveStreamCasino = $gameLiveStreamCasino->pluck('gameId')->contains($partner_provider . '_' . $id);
    $liveCasinoData = null;
    $providerLogo = null;
    if ($isGameLiveStreamCasino || $isRenderLiveCasino) {
        $tags = 'live';
        $liveCasinoData = $gameLiveStreamCasino->where('gameId', $partner_provider . '_' . $id)->first();
        $providerLogo = 'asset/icons/home/<USER>/' . strtolower($partner_provider) . '.webp';
    }
    $jackpotId = match ($partner) {
        'rik', 'b52', 'go', '789club' => $partner . '_' . $id,
        default => $id,
    };
    $jackpotId = $jsJackpotId ? $jsJackpotId : $jackpotId;
    $maintain = $game->maintain ?? false ? 'true' : '';
    $image = html_entity_decode($image);
@endphp

<div
    class="video-wrapper uicard game-item js-game-card-item js-game-item-wrapper relative w-full cursor-pointer overflow-hidden xl:rounded-lg rounded-md [&:not(:has(.jackpot-wrapper.hidden))_.game-item-thumbnail]:rounded-tr-[10px]"
    {{ $attributes->except(['image', 'name', 'id']) }}
    data-game="@json($game)"
    data-tags="{{ $tags }}"
    data-jackpot-id="{{ $id }}"
    data-game-id="{{ $id }}"
    data-name="{{ $name }}"
    data-image="{{ $image }}"
    data-type="{{ $type }}"
    data-api-url="{{ $game->api_url ?? '' }}"
    data-partner="{{ $game->partner ?? ($partner ?? '') }}"
    data-maintain="{{ $maintain }}"
    onclick="openGame(this, event)"
>
    <div
        class="js-game-card-group group peer relative xl:h-auto xl:max-h-fit xl:w-auto"
    >
        <div
            @class([
                "card-label absolute top-0 xl:left-[14px] left-2 z-[2]",
                $cardLabelClass
            ])
        >
            @if ((isset($tags) && $tags) || (isset($game) && isset($game->tags) && $game->tags))
                <x-kit.label
                    type="{{ strtolower($tags ?: $game->tags ?? '') }}"
                    direction="horizontal"
                    size="{{ isset($game->tags) && $game->tags === 'event' ? 'event' : 'custom' }}"
                    :isMbResponsive="$isMbResponsive"
                ></x-kit.label>
            @endif
        </div>

        <div
            class="jackpot-wrapper js-jackpot-value-{{ $jackpotId }} absolute right-[-0.5px] top-0 z-[2] items-center justify-center gap-[5px] xl:rounded-bl-lg xl:rounded-tr-lg rounded-bl-md rounded-tr-md bg-black-50 px-[6px] py-[2px] h-[18px] xl:h-[25px] backdrop-blur-lg xl:px-2.5 xl:py-[1px]  {{ $jackpotWrapperClass }} {{ $jackpotValue > 0 ? 'flex' : 'hidden'}}">
            <div class="uicard__jackpot">
                <img src="{{ asset('asset/images/home/<USER>') }}" alt="coin" class="h-3 w-3 min-w-3 xl:h-3.5 xl:w-3.5 xl:min-w-3.5">
            </div>
            <div
                @class([
                    'js-jackpot-value uicard__jackpot--value font-normal leading-[calc(18/12)] text-secondary-600 max-xl:text-[10px] xl:text-sm xl:font-medium xl:leading-[calc(20/14)]',
                    'max-xxs:text-[8px] max-xl:text-[9px] jackpot-width-small' => $isMbResponsive,
                    'jackpot-width-normal' => !$isMbResponsive,
                    $jackpotClass
                ])
            >
                {{ formatAmount($jackpotValue) ?? 0 }}
            </div>
        </div>

        <div
            class="absolute inset-0 z-[11] hidden aspect-square h-full w-full items-start justify-center rounded-lg opacity-0 transition-opacity duration-300 group-hover:opacity-100 xl:flex"
        >
            <div
                class="flex h-full w-full items-center justify-center rounded-lg bg-black-700 bg-[url('/public/asset/images/games/bg-hover-card.avif')] bg-cover bg-center opacity-100 backdrop-blur-[4px] transition-opacity duration-300 xl:opacity-0 xl:group-hover:opacity-100">
                <div class="flex items-center justify-center">
                    <x-kit.button
                        size="small"
                        type="gradient-secondary"
                        rightIcon="icon-effect-arrow"
                        rightIconClass="!text-[20px]"
                        class="!w-[110px]"
                    >
                        Chơi Ngay
                    </x-kit.button>
                </div>
            </div>
        </div>

        <div
            @class([
                'relative game-item-thumbnail aspect-[1/1] [&_video]:pointer-events-none',
            ])
            data-thumbnail="{{ $image ?? asset('asset/images/games/game-default.webp') }}"
        >
            {{-- provider logo --}}
            @if ($providerLogo)
                <div class="absolute bottom-[5px] right-[5px] flex h-[31px] items-center gap-1 max-xl:h-[15%]">
                    <img src="{{ asset($providerLogo) }}" class="h-full object-cover" loading="lazy" alt="{{ $partner }}" />
                </div>
            @endif
            @if ($isGameLiveStreamCasino || $isRenderLiveCasino)
                {{-- sound --}}
                <div
                    class="js-toggle-sound hidden !pointer-events-auto absolute left-1 top-1 z-[2] h-[22px] w-[22px] cursor-pointer items-center justify-center rounded-full bg-black-90 text-neutral">
                    <i class="icon-unvolume text-[14px]"></i>
                </div>
                {{-- viewers --}}
                <div
                    data-game-id="{{ $id }}"
                    data-game-partner="{{ $partner }}"
                    class="hidden js-item-viewers js-{{$partner}}-viewers absolute bottom-[9px] right-[9px] z-[2] !pointer-events-auto w-[52px] h-[19px] xl:h-6 xl:min-w-[55px] min-w-[52px] bg-[#FFFFFF33] backdrop-blur-[3px] rounded-sm px-2 flex items-center justify-center gap-1 text-neutral js-live-game-item-preview-viewers"
                >
                    <i class="icon-group-user"></i>
                    <span class="text-[12px] font-bold js-viewers">{{ $gameList[0]->viewers ?? 0 }}</span>
                </div>
            @endif
            <div
                @if ($isGameLiveStreamCasino || $isRenderLiveCasino)
                    id="{{ $partner_provider . '_' . $id }}"
                    data-thumbnail="{{ $image }}"
                @endif
                @class([
                    'relative aspect-[1/1] overflow-hidden rounded-lg bg-neutral-790 !pointer-events-none',
                    'js-live-game-item' => $isGameLiveStreamCasino || $isRenderLiveCasino,
                    'live-game-square' => $isLiveGameSquare,
                ])
            >
                <div class="skeleton-loader absolute inset-0 aspect-[1/1]"></div>
                <img
                    src="{{ $image }}" alt="{{ $name }}"
                    onload="setTimeout(() => {
                        this.classList.add('loaded');
                        this.previousElementSibling.classList.add('hidden');
                    }, 200);"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/games/game-default.webp') }}'; this.previousElementSibling.classList.add('hidden');"
                    class="lazy-image absolute hidden aspect-[120/120] h-full w-full rounded-lg object-cover"
                >
            </div>
        </div>
    </div>
    <div @class([
        'uicard__info mt-1 flex justify-between xl:w-full gap-1 pr-[1px] peer-hover:[&_.tailwind-peer-text]:!text-primary',
        'items-center' => $isFavoriteCenter,
    ])>
        <img src="{{ asset('asset/images/games/game-new.avif') }}" alt="game-new" @class([
            'absolute left-[14px] top-0 hidden w-[28px] h-[18px]',
            '!block ' => isset($game) && is_object($game) && $game->is_new,
        ])>
        <div class="uicard__info--name flex flex-1 cursor-default flex-col gap-0.5 overflow-hidden">
            <div class="flex flex-col gap-0.5">
                <div
                    class="uicard__info--name-click tailwind-peer-text line-clamp-1 cursor-pointer text-xs font-medium capitalize text-neutral xl:text-[14px] xl:leading-[18px] {{ $nameClass }}"
                >
                    {{ html_entity_decode($name) }}
                </div>
            </div>
            <div class="flex items-center gap-[2px] xl:gap-[4px]">
                <div class="uicard__info--provider text-[10px] leading-[16px] text-neutral-300 {{ $providerClass }}">
                    {{ $game->partner_txt ?? ($provider ?? '') }}
                </div>
            </div>
        </div>

        <div
            @class([
                'cursor-pointer js-game-favorite text-neutral',
                'text-primary-500' =>
                    ($game && isset($game->is_favorite) && $game->is_favorite) ||
                    $favorite === 'favorite',
                'hidden xl:block' => $page === 'home',
            ])
            data-game-id="{{ $id }}"
            data-table-id="{{ $tableId ?? '' }}"
            data-name="{{ $name }}"
            data-type="{{ $type }}" 
            data-provider="{{ $game->partner ?? ($partner ?? '') }}"
        >
            <i class="{{ ($game && isset($game->is_favorite) && $game->is_favorite) || $favorite === 'favorite' ? 'icon-favorited' : 'icon-unfavorite' }} text-[18px]"></i>
            <img src={{ asset('asset/images/spinner.svg') }} alt="spinner" class="loading-icon hidden w-[18px] h-[18px]">
        </div>

        @if ($page === 'home')
            <div @class([
                'min-w-[16.5px] text-xs cursor-pointer js-game-favorite mt-1 block xl:hidden',
                'text-primary-500' =>
                    ($game && isset($game->is_favorite) && $game->is_favorite) ||
                    $favorite === 'favorite',
            ]) data-game-id="{{ $id }}" data-name="{{ $name }}"
                data-type="{{ $type }}" data-provider="{{ $game->partner ?? ($partner ?? '') }}">
                <i class="{{ ($game && isset($game->is_favorite) && $game->is_favorite) || $favorite === 'favorite' ? 'icon-favorited' : 'icon-unfavorite' }}"></i>
                <img src={{ asset('asset/images/spinner.svg') }} alt="spinner" class="loading-icon hidden w-[18px] h-[18px]">
            </div>
        @endif
    </div>
</div>
@pushOnce('scripts')
    @vite(['resources/js/game/card.js'])
@endpushOnce

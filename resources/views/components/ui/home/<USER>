@props([
    'swiperConfig' => null,
    'list' => [],
    'class' => '',
])

@if (!empty($swiperConfig) && count($list) > 0)
    <div class="hero-banner pt-[8px] pb-[12px] px-[10px] xl:pb-[60px] {{ $class }}" x-data="{}">
        <div class="hero-banner__wrapper aspect-[370/110]">
            <x-kit.swiper :swiperConfig="$swiperConfig" swiperRequiredClass="heroSwiper">
                @foreach ($list as $key =>$item)
                    <div class="swiper-slide">
                        @if (isset($item['checkAuth']) && $item['checkAuth'] && !Auth::check())
                            <button
                                type="button"
                                aria-label="hero-banner"
                                onclick="openLogin()"
                                class="cursor-pointer">
                                <picture>
                                    <source media="(min-width: 1200px)" srcset="{{ asset($item['image']) }}" type="image/webp">
                                    <img src="{{ asset(path: $item['image-mb']) }}" class="w-full" alt="hero-banner" loading="{{ $key !== 0 ? 'lazy' : 'eager'}}"/>
                                </picture>
                            </a>
                        @else
                            <a 
                                href="{{ $item['link'] }}" 
                                aria-label="hero-banner"
                                class="cursor-pointer">
                                <picture>
                                    <source media="(min-width: 1200px)" srcset="{{ asset($item['image']) }}" type="image/webp">
                                    <img src="{{ asset(path: $item['image-mb']) }}" class="w-full" alt="hero-banner" loading="{{ $key !== 0 ? 'lazy' : 'eager'}}"/>
                                </picture>
                            </a>
                        @endif
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
    </div>
@endif

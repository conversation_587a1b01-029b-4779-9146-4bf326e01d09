@php
    $fixedThumbnail = config('home.fixedThumbnail');

    $gameList = collect($gameList)->sort(function ($a, $b) {
        $order = [
            'vingame' => 0,
            'rik' => 1,
            'b52' => 2,
            'go' => 3,
        ];

        $aOrder = isset($order[$a->partner_provider]) ? $order[$a->partner_provider] : 3;
        $bOrder = isset($order[$b->partner_provider]) ? $order[$b->partner_provider] : 3;

        return $aOrder - $bOrder;
    })->toArray();

    $vinGameProvider = 'vingame';
    $gameLiveMb = head(array: $gameList);
    $gameListMb = array_filter($gameList, function($game) use ($gameLiveMb) {
        return $game->partner_game_id !== $gameLiveMb->partner_game_id;
    });
@endphp

@pushOnce('preloadLink')
    @foreach ($gameList as $game)
        <link rel="preload" href="{{ asset($game->image ?? '') }}" as="image" type="image/webp">
        <link rel="preload" href="{{ asset($game->image_mobile ?? '') }}" as="image" type="image/webp">
    @endforeach
@endPushOnce
<div class="{{ !isset($type) || $type !== 'lobby' ? 'container' : ''}}">
    @if (!isset($type) || $type !== 'lobby')
        <div class="flex justify-between">
            <div
                @class([
                    "flex items-center xl:gap-3 gap-1 mr-auto relative pl-3 xl:pl-6 pr-[32px] xl:pr-[40px] rounded-[4px_0_0_4px] xl:rounded-[6px_0_0_6px] flex items-center bg-title-gradient-primary text-[18px] font-[400] h-[33px] xl:h-[53px] font-redzone leading-[18px] font-normal text-neutral uppercase xl:text-[24px] xl:leading-[28px]",
                    "before:content-[''] before:absolute before:bg-primary-400 before:rounded-[0_6px_6px_0] before:z-[1] before:w-[3px] xl:before:w-[5px] before:h-[22px] xl:before:h-[30px] before:left-0 before:top-1/2 before:-translate-y-1/2"
                ])
            >
                <img src="{{ asset('/asset/icons/home/<USER>/live.svg') }}" class="xl:h-[27px] h-[17px]" loading="lazy" alt="live" />
                <span class="text-neutral xl:text-[24px] font-redzone uppercase xl:leading-[28px] text-[18px] leading-[22px]">
                    CASINO TRỰC TIẾP
                </span>
            </div>
            <a
                class="xl:hidden cursor-pointer text-xs leading-[18px] font-medium xl:text-[14px] xl:leading-[20px] text-primary hover:text-primary-300 ml-auto flex items-center gap-1.5 capitalize"
                href="/song-bai-livecasino-truc-tuyen"
            >
                {{ __('common.see_more') }}
                <i class="icon-arrow-right group-hover:text-primary-300 text-primary text-[20px]"></i>
            </a>
        </div>
    @endif
    <div
        class="relative aspect-[1240/223] mt-[18px] hidden xl:flex pl-[30px] py-6 rounded-lg bg-[url('/public/asset/images/home/<USER>/section-live-casino-bg.avif')] bg-cover bg-center bg-no-repeat"
    >
        <a
            class="max-xl:hidden group absolute bottom-[calc(100%+18px)] right-0 cursor-pointer text-xs leading-[18px] font-normal text-primary hover:text-primary-300 ml-auto flex items-center gap-1.5 capitalize"
            href="/song-bai-livecasino-truc-tuyen"
        >
            {{ __('common.see_more') }}
            <i class="icon-arrow-right group-hover:text-primary-300 text-primary text-[20px]"></i>
        </a>
        <div
            @class([
                "flex-1 flex gap-[18px] js-live-game-list js-home-live-game-list",
            ])
        >
            @foreach (array_slice($gameList ?? [], 0, 3) as $key => $game)
                <div
                    class="cursor-pointer flex-1 aspect-[300/174] rounded-lg relative js-game-item-wrapper !select-none"
                    data-game-id="{{ $game->partner_game_id ?? '' }}"
                    data-name="{{ $game->name ?? '' }}"
                    data-image="{{ $game->image ?? '' }}"
                    data-type="casino"
                    data-api-url="{{ $game->api_url ?? '' }}"
                    data-partner="{{ $game->partner ?? '' }}"
                    onclick="openGame(this, event)"
                >
                    {{-- thumb --}}
                    <div class="absolute inset-0 top-0 left-0 w-full h-full rounded-lg overflow-hidden bg-black z-[1]">
                        {{-- <img src="{{ $game->image }}" alt="{{ $game?->name ?? '' }}" class="w-full h-full object-cover object-top" /> --}}
                        <div
                            id="{{ $game->partner_provider . '_' . $game->partner_game_id }}"
                            data-thumbnail="{{ $game->image }}"
                            class="js-live-game-item relative w-full h-full !pointer-events-none"
                        >
                            <div class="skeleton-loader !absolute inset-0 aspect-[300/174] !bg-cover pointer-events-none"></div>
                            <img
                                src="{{ $game->image }}"
                                alt="{{ $game?->name ?? '' }}"
                                loading="lazy"
                                onload="setTimeout(() => {
                                    this.classList.add('loaded');
                                    this.previousElementSibling.classList.add('hidden');
                                }, 200);"
                                onerror="this.onerror=null; this.src='{{ asset('asset/images/games/game-default.webp') }}'; this.previousElementSibling.classList.add('hidden');"
                                class="lazy-image absolute top-0 left-0 hidden aspect-[300/174] h-full max-w-full xl:rounded-lg rounded-md object-contain object-top main-thumbnail"
                            />
                        </div>
                    </div>

                    <div class="absolute top-2 left-2 z-[2]">
                        <div class="flex items-center gap-2 z-[2]">
                            {{-- favorite --}}
                            <div
                                class="js-game-favorite w-[22px] h-[22px] flex justify-center items-center bg-[#00000066] rounded-full"
                                data-game-id="{{ $game->partner_game_id }}"
                                data-table-id="{{ $game->table_id ?? '' }}"
                                data-name="{{ $game->name }}"
                                data-type="casino"
                                data-provider="{{ $game->partner ?? '' }}"
                            >
                                <i
                                    @class([
                                        "text-[14px] text-neutral",
                                        "icon-favorited text-primary" => $game->is_favorite,
                                        "icon-unfavorite" => !$game->is_favorite,
                                    ])
                                ></i>
                                <img src={{ asset('asset/images/spinner.svg') }} alt="spinner" class="loading-icon hidden w-[18px] h-[18px]">
                            </div>
                            {{-- sound --}}
                            <div class="cursor-pointer js-toggle-sound text-neutral !pointer-events-auto h-[22px] w-[22px] bg-[#00000066] rounded-full flex items-center justify-center">
                                <i class="icon-unvolume text-[14px]"></i>
                            </div>
                        </div>
                    </div>

                    <div class="absolute right-2 top-2 z-[2]">
                        <div class="flex items-center gap-2 bg-[#00000080] w-fit h-[20px] rounded-full relative z-[2] px-2 py-[2px]">
                            {{-- icon live --}}
                            <div class="flex items-center justify-center gap-1">
                                <x-ui.live-icon />
                                <div class="text-neutral text-xs font-semibold uppercase">Live</div>
                            </div>
                            {{-- viewer --}}
                            <div
                                data-game-id="{{ $game->partner_game_id }}"
                                data-game-partner="{{ $game->partner }}"
                                class="hidden js-item-viewers js-{{$game->partner}}-viewers !pointer-events-auto flex items-center justify-center gap-2 text-neutral js-live-game-item-preview-viewers"
                            >
                                <div class="relative w-[1px] h-[16px] border-r flex-shrink-0 block opacity-100" style="border-color: #3E4049"></div>
    
                                <div class="flex items-center gap-1">
                                    <i class="icon-group-user text-[14px]"></i>
                                    <span class="js-viewers text-xs font-semibold leading-4">{{ $game->viewers ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {{-- provider --}}
                    @if (isset($game->partner))
                        <div class="absolute z-[3] bottom-2 right-2 h-[24px] flex justify-center items-center" style="bottom: 8px; right: 8px;">
                            <img src="{{ asset('asset/icons/home/<USER>/' . strtolower($game->partner) . '.webp') }}"
                                class="object-contain w-auto h-[24px]" loading="lazy" alt="{{ $game->partner }}" />
                        </div>
                    @endif

                    {{-- content --}}
                    <div class="absolute z-[2] bottom-0 left-0 w-full px-[13px] py-2.5 flex items-end rounded-b-[7px] bg-[linear-gradient(0deg,_#21232A_0%,_rgba(33,35,42,0)_100%)]">
                        <div class="overflow-hidden flex-1">
                            <span class="text-sm text-neutral font-medium block truncate leading-[18px]">
                                {{ $game->name ?? '' }}
                            </span>

                            {{-- jackpot --}}
                            @if (isset($game->specialLabel))
                                <div
                                    @class([
                                        'flex gap-1 bg-[#00000080] mt-[6px] w-max rounded-sm overflow-hidden backdrop-blur-[3px] px-2 py-[5px] items-center',
                                        $game->specialLabel['labelClass'] ?? '',
                                    ])
                                    data-game-id="{{ $game->partner }}_{{ $game->partner_game_id }}"
                                >
                                    <span class="text-[10px] leading-[16px] font-medium whitespace-nowrap js-jackpot-home-value home-live-jackpot-label">
                                        {!! $game->specialLabel['content'] !!}
                                    </span>
                                    @if (isset($game->specialLabel['rightIcon']) && !empty($game->specialLabel['rightIcon']))
                                        <img 
                                            src="{{ asset($game->specialLabel['rightIcon']) }}" 
                                            loading="lazy" 
                                            alt="ic-jackpot"
                                            @class([
                                                'w-[33px] h-[20px]',
                                                $game->specialLabel['rightIconClass'] ?? '',
                                            ])
                                        />
                                    @endif
                                </div>
                            @else
                                <div
                                    class="bg-[#00000080] mt-[6px] px-2 w-max rounded-sm overflow-hidden backdrop-blur-[3px] py-1 gap-[3px] items-center js-jackpot-value-{{ $game->partner }}_{{ $game->partner_game_id }} {{ (isset($game->jackpot) && (float) $game->jackpot > 0 )? 'flex' : 'hidden' }}"
                                    data-game-id="{{ $game->partner }}_{{ $game->partner_game_id }}"
                                >
                                    <img src="{{ asset('asset/images/home/<USER>') }}" class="w-3.5 h-3.5"
                                        loading="lazy" alt="ic-jackpot" />
                                    <span class="text-[12px] leading-[16px] whitespace-nowrap text-secondary-600 js-jackpot-home-value">
                                        {{ isset($game->jackpot) && ((float) $game->jackpot > 0) ? formatAmount($game->jackpot) : '' }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="w-[calc((283/1240)*100%)] shrink-0 relative pointer-events-none">
            <img
                src="{{ asset('/asset/images/home/<USER>/live-casino-model.avif') }}"
                alt="live-casino-model"
                class="w-full absolute bottom-[-24px] right-0"
            >
        </div>
    </div>
    <div class="xl:hidden js-live-game-list-mb mt-[12px]">
        @if (isset($gameList) && count($gameList) > 0)
        @php
            @endphp
            <div
                class="cursor-pointer mb-3 flex-1 aspect-[359/207] rounded-lg overflow-hidden relative js-game-item-wrapper !select-none"
                data-game-id="{{ $gameLiveMb->partner_game_id ?? '' }}"
                data-name="{{ $gameLiveMb->name ?? '' }}"
                data-image="{{ $gameLiveMb->image ?? '' }}"
                data-thumbnail="{{ $gameLiveMb->image ?? '' }}"
                data-type="casino"
                data-api-url="{{ $gameLiveMb->api_url ?? '' }}"
                data-partner="{{ $gameLiveMb->partner ?? '' }}"
                onclick="openGame(this, event)"
            >
                {{-- thumb --}}
                <div class="absolute inset-0 top-0 left-0 w-full h-full rounded-lg overflow-hidden bg-black z-[1]">
                    {{-- <img src="{{ $gameLiveMb->image }}" alt="{{ $gameLiveMb?->name ?? '' }}" class="w-full h-full object-cover object-top main-thumbnail" /> --}}
                    <div
                        id="{{ $gameLiveMb->partner_provider . '_' . $gameLiveMb->partner_game_id. '_mobile' }}"
                        data-thumbnail="{{ $gameLiveMb->image }}"
                        class="js-live-game-item relative w-full h-full !pointer-events-none"
                    >
                        <div class="skeleton-loader !absolute inset-0 aspect-[359/207] !bg-cover pointer-events-none"></div>
                        <img
                            src="{{ $gameLiveMb->image }}"
                            alt="{{ $gameLiveMb?->name ?? '' }}"
                            loading="lazy"
                            onload="setTimeout(() => {
                                this.classList.add('loaded');
                                this.previousElementSibling.classList.add('hidden');
                            }, 200);"
                            onerror="this.onerror=null; this.src='{{ asset('asset/images/games/game-default.webp') }}'; this.previousElementSibling.classList.add('hidden');"
                            class="lazy-image absolute top-0 left-0 hidden aspect-[359/207] h-full max-w-full xl:rounded-lg rounded-md object-contain object-top main-thumbnail"
                        >
                    </div>
                </div>

                <div class="absolute top-2 left-2 z-[2]">
                    <div class="flex items-center gap-2 z-[2]">
                        {{-- favorite --}}
                        <div
                            class="js-game-favorite w-[22px] h-[22px] flex justify-center items-center bg-[#00000066] rounded-full"
                            data-game-id="{{ $gameLiveMb->partner_game_id }}"
                            data-table-id="{{ $gameLiveMb->table_id ?? '' }}"
                            data-name="{{ $gameLiveMb->name }}"
                            data-type="casino"
                            data-provider="{{ $gameLiveMb->partner ?? '' }}"
                        >
                            <i
                                @class([
                                    "text-[14px] text-neutral",
                                    "icon-favorited text-primary" => $gameLiveMb->is_favorite,
                                    "icon-unfavorite" => !$gameLiveMb->is_favorite,
                                ])
                            ></i>
                            <img src={{ asset('asset/images/spinner.svg') }} alt="spinner" class="loading-icon hidden w-[18px] h-[18px]">
                        </div>
                        {{-- sound --}}
                        <div class="cursor-pointer js-toggle-sound text-neutral !pointer-events-auto h-[22px] w-[22px] bg-[#00000066] rounded-full flex items-center justify-center">
                            <i class="icon-unvolume text-[14px]"></i>
                        </div>
                    </div>
                </div>
                
                <div class="absolute right-2 top-2 z-[2]">
                    <div class="flex items-center gap-2 bg-[#00000080] w-fit h-[20px] rounded-full relative z-[2] px-2 py-[2px]">
                        {{-- icon live --}}
                        <div class="flex items-center justify-center gap-1">
                            <x-ui.live-icon />
                            <div class="text-neutral text-xs font-semibold uppercase">Live</div>
                        </div>
                        {{-- viewer --}}
                        <div
                            data-game-id="{{ $gameLiveMb->partner_game_id }}"
                            data-game-partner="{{ $gameLiveMb->partner }}"
                            class="hidden js-item-viewers js-{{$gameLiveMb->partner}}-viewers !pointer-events-auto flex items-center justify-center gap-2 text-neutral js-live-game-item-preview-viewers"
                        >
                            <div class="relative w-[1px] h-[16px] border-r flex-shrink-0 block opacity-100" style="border-color: #3E4049"></div>

                            <div class="flex items-center gap-1">
                                <i class="icon-group-user text-[14px]"></i>
                                <span class="js-viewers text-xs font-semibold leading-4">{{ $gameLiveMbList[0]->viewers ?? 0 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                {{-- provider --}}
                @if (isset($gameLiveMb->partner))
                    <div class="absolute bottom-2 right-2 z-[3] h-[31px] flex justify-center items-center" style="bottom: 8px; right: 8px;">
                        <img src="{{ asset('asset/icons/home/<USER>/' . strtolower($gameLiveMb->partner) . '.webp') }}"
                            class="object-contain w-auto h-[24px]" loading="lazy" alt="{{ $gameLiveMb->partner }}" />
                    </div>
                @endif
                {{-- content --}}
                <div class="absolute z-[2] bottom-0 left-0 w-full p-2 flex items-end bg-[linear-gradient(0deg,_#21232A_0%,_rgba(33,35,42,0)_100%)]">
                    <div class="overflow-hidden flex-1">
                        <span class="text-sm text-neutral font-medium block truncate leading-[18px]">
                            {{ $gameLiveMb->name ?? '' }}
                        </span>

                        {{-- jackpot --}}
                        {{-- If has vingame, show static content--}}
                        @if($gameLiveMb->partner_provider == $vinGameProvider)
                            <div
                                class="flex gap-1 bg-[#00000080] mt-[6px] w-max rounded-sm overflow-hidden backdrop-blur-[3px] px-2 py-1 items-center"
                                data-game-id="{{ $game->partner }}_{{ $game->partner_game_id }}"
                            >
                                <span class="text-[12px] leading-[16px] font-medium whitespace-nowrap text-secondary-600 js-jackpot-home-value">
                                    Cơ hội thắng
                                </span>
                                <img src="{{ asset('asset/images/home/<USER>/bau_cua_bigwin_rate.webp') }}" class="w-[33px] h-[20px]"
                                    loading="lazy" alt="ic-jackpot" />
                            </div>
                        @else
                            <div
                                class="bg-[#00000080] mt-[6px] px-2 w-max rounded-sm overflow-hidden backdrop-blur-[3px] py-1 gap-[3px] items-center js-jackpot-value-{{ $gameLiveMb->partner }}_{{ $gameLiveMb->partner_game_id }} {{ (isset($gameLiveMb->jackpot) && (float) $gameLiveMb->jackpot > 0) ? 'flex' : 'hidden' }}"
                                data-game-id="{{ $gameLiveMb->partner }}_{{ $gameLiveMb->partner_game_id }}"
                            >
                                <img src="{{ asset('asset/images/home/<USER>') }}" class="w-3.5 h-3.5"
                                    loading="lazy" alt="ic-jackpot" />
                                <span class="text-[12px] leading-[16px] whitespace-nowrap text-secondary-600 js-jackpot-home-value">
                                    {{ isset($gameLiveMb->jackpot) && ((float) $gameLiveMb->jackpot > 0) ? formatAmount($gameLiveMb->jackpot) : '' }}
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            <div class="flex flex-nowrap gap-3 overflow-x-auto no-scrollbar -mx-4 px-4">
                @foreach ($gameListMb as $game)
                    <div class="w-[calc(100%/(359/120))] shrink-0 grow-1">
                        <x-ui.card :game="$game" name="{{ $game->name ?? 'title' }}" image="{{ $game->image_mobile ?? '' }}"
                            type="casino"
                            data-api="{{ $game->api_url ?? '' }}"
                            id="{{ $game->partner_game_id ?? '' }}"
                            jackpotValue="{{ $game->jackpot ?? 0 }}"
                            favorite="{{ isset($game->is_favorite) && $game->is_favorite ? 'favorite' : '' }}"
                            provider="{{ $game->partner_txt ?? '' }}"
                            partner="{{ $game->partner ?? '' }}"
                            tags="live"
                            class="flex flex-col items-center text-marron loader-image-transparent"
                            cardLabelClass="!left-1"
                            isMbResponsive
                        />
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</div>
@pushOnce('scripts')
    @vite('resources/js/home/<USER>')
@endPushOnce

@props([
    'title' => '',
    'titleButton' => '',
    'link' => '',
    'class' => null,
    'swiperConfig' => null,
    'swiperRequiredClass' => null,
    'list' => [],
    'totalJackpot' => 0,
])

@php
    $lotterySection = config('home.lotterySection');
@endphp

<div @class(['w-full flex justify-center container', $class])>
    <div class="w-full max-w-desktop">
        <x-ui.title-section title="{{ $title }}" titleButton="{{ $titleButton }}" link="{{ $link }}"
            class="mb-3 xl:mb-5"></x-ui.title-section>
        <div class="block xl:hidden">
            <div class="grid grid-cols-3 xl:gap-[8px] gap-[4px] max-xl:gap-[8px] mb-3 xl:mb-0 xl:grid-cols-[130px_130px_130px]">
                @foreach ($lotterySection['sortList'] as $key => $item)
                <div>
                    <x-kit.button
                        @class([
                            'button-lottery-mobile-active max-xl:text-xs rounded-[4px] w-full px-0 capitalize [&_i]:text-[20px] max-xl:[&_i]:text-[16px] button-lottery-mobile-' . $item['value'],
                            $key===0 ? 'flex' : 'hidden' ,
                        ])
                        style="filled"
                        type="primary"
                        size="medium"
                        leftIcon="{{ $item['icon'] }}"
                        leftIconClass="max-xl:text-[16px]"
                    >
                        {{ $item['label'] }}
                    </x-kit.button>
                    <x-kit.button
                        onclick="handleClickFilter(event, 'mobile')"
                        data-type='mobile'
                        data-value="{{ $item['value'] }}"
                        data-target="{{ 'button-lottery-mobile-' . $item['value'] }}"
                        @class([
                            'button-lottery-mobile bg-neutral-650 w-full px-0 max-xl:text-xs capitalize [&_i]:text-[20px] max-xl:[&_i]:text-[16px] rounded-[4px]',
                            $key===0 ? 'hidden' : 'flex' ,
                        ])
                        style="filled"
                        type="tertiary"
                        size="medium"
                        leftIcon="{{ $item['icon'] }}"
                        leftIconClass="text-neutral-300"
                    >
                        {{ $item['label'] }}
                    </x-kit.button>
                </div>
                @endforeach
            </div>
            <div class="mb-3">
                <a href="/cong-game/no-hu" class="relative h-[121px] w-full block bg-[url('/public/asset/images/home/<USER>/banner-mb.avif')] bg-cover bg-center bg-no-repeat rounded-lg overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/jackpot-bling-mb.avif') }}" alt="banner" class="aspect-[216/121] absolute bottom-0 left-0 h-full" />
                    <img src="{{ asset('asset/images/home/<USER>/jackpot-model-mb.avif') }}" alt="banner" class="aspect-[216/121] absolute bottom-0 left-0 h-full z-[2]" />
                    <div class="absolute top-[19px] right-3 flex flex-col w-max z-[1]">
                        <img src="{{ asset('asset/images/home/<USER>/jackpot.avif') }}" alt="jackpot" class="w-full mb-3 max-w-[160px] aspect-[160/35] ml-auto mr-[14px]"/>
                        <div class="min-w-[214px] w-max h-[36px]">
                            <div class="flex justify-center items-center gap-[4px] w-full h-full pl-[8px] rounded-[8px] bg-[url('/public/asset/images/home/<USER>/jackpot-number-bg.avif')] bg-cover bg-center bg-no-repeat">
                                <img src="{{ asset('asset/images/home/<USER>/coin.webp') }}" alt="coin" class="w-[21px] h-[21px]"/>
                                <p class="js-total-jackpot home-jackpot-text font-bold min-w-[120px] jackpot-money text-[22px] leading-[22px] text-transparent bg-gradient-jackpot-money bg-clip-text">
                                    {{ ((float) $totalJackpot > 0) ? formatAmount($totalJackpot) : '' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="card-group flex flex-nowrap gap-3 overflow-x-auto no-scrollbar -mx-4 px-4">
                @foreach ($list as $item)
                    <div class="w-[calc(100%/(359/120))] shrink-0 grow-1">
                        <x-ui.card
                            name="{{ $item->name ?? 'title' }}"
                            image="{{ $item->image ?? '' }}"
                            :game="$item"
                            type="game"
                            data-api="{{ $item->api_url ?? '' }}"
                            id="{{ $item->partner_game_id ?? '' }}"
                            tableId="{{ $item->table_id ?? '' }}"
                            jackpotValue="{{ $item->jackpot ?? 0 }}"
                            provider="{{ $item->partner_txt ?? '' }}"
                            partner="{{ $item->partner ?? '' }}"
                            favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                            class="flex flex-col items-center text-marron loader-image-transparent"
                            tags="{{ $item -> tags ?? '' }}"
                            cardLabelClass="max-xl:left-[8px]"
                            jackpotWrapperClass="max-xl:py-[1px] max-xl:px-1 [&>div]:max-xl:leading-4 max-xl:gap-[2px] max-xl:rounded-bl-[8px]"
                            jackpotClass="js-jackpot-card-in-home-value"
                            isMbResponsive
                        >
                        </x-ui.card>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="hidden gap-[20px] xl:grid xl:grid-cols-[376fr_884fr]">
            <a href="/cong-game/no-hu" class="relative">
                <img src="{{ asset('asset/images/home/<USER>/jackpot-banner.avif') }}" alt="banner" class="max-w-[376px] aspect-[376/552] rounded-lg" loading="lazy" />
                <div class="absolute bottom-6 left-0 flex flex-col w-full">
                    <div class="w-full px-6">
                        <div class="flex justify-start items-center gap-[7px] h-[56px] w-full pl-[30px] pr-[29px] rounded-[8px] bg-[url('/public/asset/images/home/<USER>/jackpot-number-bg.avif')] bg-cover bg-center bg-no-repeat">
                            <img src="{{ asset('asset/images/home/<USER>/coin.webp') }}" alt="coin" class="w-[32px] h-[32px]"/>
                            <p class="js-total-jackpot home-jackpot-text font-bold min-w-60 jackpot-money text-[30px] leading-[30px] text-transparent bg-gradient-jackpot-money bg-clip-text">
                                {{ ((float) $totalJackpot > 0) ? formatAmount($totalJackpot) : '' }}
                            </p>
                        </div>
                    </div>
                </div>
            </a>
            <div class="flex flex-col gap-5 flex-grow">
                <div class="flex justify-between items-center">
                    <div class="menu-lottery-home flex xl:gap-2 gap-1 mb-1.5 xl:mb-0">
                        @foreach ($lotterySection['sortList'] as $key => $item)
                        <div>
                            <x-kit.button
                                @class([
                                    'button-lottery-active px-4 h-10 [&_i]:!text-[20px] [&_i]:!leading-[20px] capitalize flex text-sm button-lottery-' . $item['value'],
                                    $key===0 ? 'flex' : 'hidden' ,
                                ])
                                style="filled"
                                type="primary"
                                size="smal"
                                leftIcon="{{ $item['icon'] }}"
                            >
                                {{ $item['label'] }}
                            </x-kit.button>
                            <x-kit.button
                                onclick="handleClickFilter(event, 'pc')"
                                data-value="{{ $item['value'] }}"
                                data-target="{{ 'button-lottery-' . $item['value'] }}"
                                @class([
                                    'capitalize items-center justify-center px-4 h-10 [&_i]:!text-[20px] [&_i]:!leading-[20px] text-sm button-lottery bg-neutral-650',
                                    $key===0 ? 'hidden' : 'flex' ,
                                ])
                                style="filled"
                                type="tertiary"
                                size="smal"
                                leftIcon="{{ $item['icon'] }}"
                                leftIconClass="text-neutral-300"
                            >
                                {{ $item['label'] }}
                            </x-kit.button>
                        </div>
                        @endforeach
                    </div>
                </div>
                <div class="card-group grid grid-cols-4 gap-5">
                    @foreach ($list as $item)
                        <x-ui.card
                            name="{{ $item->name ?? 'title' }}"
                            image="{{ $item->image ?? '' }}"
                            :game="$item"
                            type="game"
                            data-api="{{ $item->api_url ?? '' }}"
                            id="{{ $item->partner_game_id ?? '' }}"
                            tableId="{{ $item->table_id ?? '' }}"
                            jackpotValue="{{ $item->jackpot ?? 0 }}"
                            provider="{{ $item->partner_txt ?? '' }}"
                            partner="{{ $item->partner ?? '' }}"
                            favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                            class="flex flex-col items-center text-marron loader-image-transparent"
                            tags="{{ $item -> tags ?? '' }}"
                        >
                        </x-ui.card>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    let timeout;
    const handleClickFilter = async (event, layout) => {
        const targetEvent = event.target;
        const {
            target,
            value,
            type
        } = targetEvent.dataset;

        const buttonList = {
            active: type === 'mobile' ? '.button-lottery-mobile-active' : '.button-lottery-active',
            click: type === 'mobile' ? '.button-lottery-mobile' : '.button-lottery',
        }

        $(buttonList.active).addClass('hidden').removeClass('flex');
        $(buttonList.click).addClass('flex').removeClass('hidden');

        $('.' + target).removeClass('hidden').addClass('flex');

        $(targetEvent).addClass('hidden');

        clearTimeout(timeout)

        await handleFilterLottery(value, layout);
    }

    const labelNew = `<x-kit.label type="new" direction="vertical" size="custom"></x-kit.label>`;
    const labelHot = `<x-kit.label type="hot" direction="vertical" size="custom"></x-kit.label>`;
    const labelLive = `<x-kit.label type="live" direction="vertical" size="custom"></x-kit.label>`;

    const handleFilterLottery = async (value, layout) => {
        try {
            if (layout === 'pc') {
                $(".card-group").replaceWith(`
                    <div class="card-group flex justify-center items-center w-full h-[400px]">
                        <div class="loading-spinner flex flex-col items-center">
                            <div class="spinner-border animate-spin w-12 h-12 border-4 rounded-full text-primary mb-4"></div>
                            <p class="text-gray-500">Đang tải dữ liệu...</p>
                        </div>
                    </div>
                `);
            }
            const data = await fetchData(
                `/game/search`, {
                    limit: 8,
                    sort: value,
                    type: "nohu",
                }, {
                    useProxy: true
                },
                ''
            );
            const {
                items,
                page: currentPage,
                totalPage
            } = data.data;

            const createElement = (game) => {
                if (game.is_favorite) {
                    return `<x-ui.card
                        name="${game.name}"
                        image="${game.image}"
                        type="game"
                        game="${JSON.stringify(game)}"
                        partner="${game.partner}"
                        data-game-id="${game.partner_game_id}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        tableId="${game.table_id ?? ''}"
                        jackpotValue="${game.jackpot ?? 0}"
                        favorite="favorite"
                        provider="${game.partner_txt}"
                        tags="${game.tags}"
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
                }

                return  `<x-ui.card
                    name="${game.name}"
                    type="game"
                    game="${JSON.stringify(game)}"
                    image="${game.image}"
                    data-api="${game.api_url}"
                    id="${game.partner_game_id}"
                    tableId="${game.table_id ?? ''}"
                    jackpotValue="${game.jackpot ?? 0}"
                    provider="${game.partner_txt}"
                    partner="${game.partner}"
                    tags="${game.tags}"
                    class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
            }

            if (layout === 'pc') {

                $(".card-group").empty();
                const newContent = `
                    <div class="card-group grid grid-cols-4 gap-5">
                        ${items.map(createElement).join('')}
                    </div>
                `;
                $(".card-group").replaceWith(newContent);
            } else {
                $(".card-group").empty();
                items.forEach((game) => {
                    const ele = createElement(game);
                    $(".card-group").append(
                        `<div class="w-[calc(100%/(359/120))] shrink-0 grow-1">
                            ${ele}
                        </div>`
                    );
                });
            }
            addLabel();
            addFavoriteGame();

        } catch (error) {
            console.log(error.message);
        }
    }

    window.addEventListener('DOMContentLoaded', () => {
        checkLabel();
    })
</script>
@endpush

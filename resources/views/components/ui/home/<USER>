@props([
    'swiperConfig' => null,
    'list' => [],
    'class' => '',
])

@if (!empty($swiperConfig) && count($list) > 0)
    <div class="hero-banner pt-[8px] pb-[12px] px-[10px] xl:pb-[60px] {{ $class }}" x-data="{}">
        <div class="hero-banner__wrapper aspect-[370/110]">
            <x-kit.swiper :swiperConfig="$swiperConfig" swiperRequiredClass="heroSwiper">
                @foreach ($list as $key =>$item)
                    <div class="swiper-slide">
                        @if (isset($item['checkAuth']) && $item['checkAuth'] && !Auth::check())
                            <button
                                type="button"
                                aria-label="hero-banner"
                                onclick="openLogin()"
                                class="cursor-pointer">
                                <picture>
                                    @php
                                        $desktopPath = pathinfo($item['image'], PATHINFO_DIRNAME) . '/' . pathinfo($item['image'], PATHINFO_FILENAME);
                                        $mobilePath = pathinfo($item['image-mb'], PATHINFO_DIRNAME) . '/' . pathinfo($item['image-mb'], PATHINFO_FILENAME);
                                    @endphp

                                    {{-- Desktop AVIF --}}
                                    @if(file_exists(public_path($desktopPath . '.avif')))
                                        <source media="(min-width: 1200px)" srcset="{{ asset($desktopPath . '.avif') }}" type="image/avif">
                                    @endif

                                    {{-- Desktop WebP --}}
                                    @if(file_exists(public_path($desktopPath . '.webp')))
                                        <source media="(min-width: 1200px)" srcset="{{ asset($desktopPath . '.webp') }}" type="image/webp">
                                    @endif

                                    {{-- Mobile AVIF --}}
                                    @if(file_exists(public_path($mobilePath . '.avif')))
                                        <source media="(max-width: 1199px)" srcset="{{ asset($mobilePath . '.avif') }}" type="image/avif">
                                    @endif

                                    {{-- Mobile WebP --}}
                                    @if(file_exists(public_path($mobilePath . '.webp')))
                                        <source media="(max-width: 1199px)" srcset="{{ asset($mobilePath . '.webp') }}" type="image/webp">
                                    @endif

                                    {{-- Fallback --}}
                                    <img
                                        src="{{ asset($item['image-mb']) }}"
                                        class="w-full"
                                        alt="hero-banner"
                                        loading="{{ $key !== 0 ? 'lazy' : 'eager'}}"
                                        decoding="async"
                                        @if($key === 0) fetchpriority="high" @endif
                                    />
                                </picture>
                            </a>
                        @else
                            <a 
                                href="{{ $item['link'] }}" 
                                aria-label="hero-banner"
                                class="cursor-pointer">
                                <picture>
                                    @php
                                        $desktopPath = pathinfo($item['image'], PATHINFO_DIRNAME) . '/' . pathinfo($item['image'], PATHINFO_FILENAME);
                                        $mobilePath = pathinfo($item['image-mb'], PATHINFO_DIRNAME) . '/' . pathinfo($item['image-mb'], PATHINFO_FILENAME);
                                    @endphp

                                    {{-- Desktop AVIF --}}
                                    @if(file_exists(public_path($desktopPath . '.avif')))
                                        <source media="(min-width: 1200px)" srcset="{{ asset($desktopPath . '.avif') }}" type="image/avif">
                                    @endif

                                    {{-- Desktop WebP --}}
                                    @if(file_exists(public_path($desktopPath . '.webp')))
                                        <source media="(min-width: 1200px)" srcset="{{ asset($desktopPath . '.webp') }}" type="image/webp">
                                    @endif

                                    {{-- Mobile AVIF --}}
                                    @if(file_exists(public_path($mobilePath . '.avif')))
                                        <source media="(max-width: 1199px)" srcset="{{ asset($mobilePath . '.avif') }}" type="image/avif">
                                    @endif

                                    {{-- Mobile WebP --}}
                                    @if(file_exists(public_path($mobilePath . '.webp')))
                                        <source media="(max-width: 1199px)" srcset="{{ asset($mobilePath . '.webp') }}" type="image/webp">
                                    @endif

                                    {{-- Fallback --}}
                                    <img
                                        src="{{ asset($item['image-mb']) }}"
                                        class="w-full"
                                        alt="hero-banner"
                                        loading="{{ $key !== 0 ? 'lazy' : 'eager'}}"
                                        decoding="async"
                                        @if($key === 0) fetchpriority="high" @endif
                                    />
                                </picture>
                            </a>
                        @endif
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
    </div>
@endif

@if (isset($depositData['p2pLink']) && $depositData['p2pLink'])
    <iframe class="iframe w-full border-0 rounded-lg" id="contentiframe" src="{{$depositData['p2pLink'] }}" scrolling="yes" allowfullscreen="" webkitallowfullscreen="true" mozallowfullscreen="true" allow="clipboard-read; clipboard-write" style="height: 1000px;"></iframe>
@else
    <div class="flex flex-col justify-between items-center">
        <img src="{{ asset('asset/images/errors/maintenance.avif') }}"
            class="max-w-[300px] xl:max-w-[530px] aspect-[300/215] xl:aspect-[530/380]"
            alt="maintenance">
        <div class="flex flex-col justify-center items-center max-w-[337px] mt-6">
            <p class="text-neutral text-[18px] leading-[22px] font-bold uppercase">
                <PERSON><PERSON> bảo trì..
            </p>
            <p class="text-[14px] leading-[18px] text-neutral text-center mt-4 mb-6">
                Website đang được bảo trì, chúng tôi sẽ sớm trở lại. Quý khách vui lòng quay lại sau.
            </p>
            <x-kit.button onclick="openLiveChat()" button-type="button" style="filled" type="gradient-secondary" size="large" class="w-[161px] flex gap-[6px] items-center capitalize">
                Liên hệ hỗ trợ
                <i class="icon-effect-arrow text-[20px]"></i>
            </x-kit.button>
        </div>
    </div>
@endif

@pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.getElementById('contentiframe');
            const iframeLoading = document.querySelector('.loading-iframe');
            const iframeWidth = $('#contentiframe').parent().width();
            if (!isMobile()) {
                iframe.style.zoom = '0.8';
            }
            iframe.onload = () => {
                iframe.classList.remove('hidden');
                iframeLoading.classList.add('hidden');
            }
        });
    </script>
@endPushOnce

{{-- define error message --}}
<div class="hidden">
    <p id="change_password_required_current_pwd">{{ __('account.change_password.required_current_pwd') }}</p>
    <p id="change_password_invalid_current_pwd">{{ __('account.change_password.invalid_current_pwd') }}</p>
    <p id="change_password_required_new_pwd">{{ __('account.change_password.required_new_pwd') }}</p>
    <p id="change_password_invalid_new_pwd">{{ __('account.change_password.invalid_new_pwd') }}</p>
    <p id="change_password_invalid_new_pwd_as_old_pwd">{{ __('account.change_password.invalid_new_pwd_as_old_pwd') }}</p>
    <p id="change_password_required_confirm_pwd">{{ __('account.change_password.required_confirm_pwd') }}</p>
    <p id="change_password_invalid_confirm_pwd">{{ __('account.change_password.invalid_confirm_pwd') }}</p>
    <p id="change_password_msg_error">{{ __('account.change_password.msg_error') }}</p>
</div>

<div class="rounded-2xl bg-[#1F232C] p-3 xl:h-full xl:p-6">
    <div class="mb-5 xl:mb-6 flex items-center gap-x-2">
        <img src="{{ asset('asset/images/account/menu/password.avif') }}" alt="password"
            class="size-[30px] object-contain" />
        <h2 class="text-[14px] font-medium capitalize leading-[18px] text-[#FFFFFF]">
            {{ __('account.change_password.title') }}</h2>
    </div>
    <form id="edit-password-form">
        <div class="space-y-4 xl:space-y-5">
            {{-- current password --}}

            <x-kit.input isRequire label="{{ __('account.change_password.current_password') }}" type="password"
                isShowIconPass autocomplete="new-password" maxlength="32" id="current_password" name="current_password"
                inputClassName="!pl-0" placeholder="{{ __('account.change_password.current_password_pld') }}">
            </x-kit.input>

            {{-- new password --}}
            <x-kit.input isRequire autocomplete="new-password" label="{{ __('account.change_password.new_password') }}"
                isShowIconPass type="password" maxlength="32" id="new_password" name="new_password"
                inputClassName="!pl-0" placeholder="{{ __('account.change_password.new_password_pld') }}">
            </x-kit.input>

            {{-- confirm password --}}
            <x-kit.input isRequire autocomplete="new-password"
                label="{{ __('account.change_password.confirm_new_password_pld_acc') }}" isShowIconPass type="password"
                maxlength="32" id="confirm_new_password" name="confirm_new_password" inputClassName="!pl-0"
                placeholder="{{ __('account.change_password.confirm_new_password_pld') }}">
            </x-kit.input>

            <x-kit.button disabled button-type="button" type="gradient-secondary" id="submit-edit-password-btn"
                class="w-full capitalize !mt-6">
                {{ __('account.update') }}
            </x-kit.button>
    </form>
</div>

@pushOnce('scripts')
    @vite('resources/js/account/change-password.js')
@endPushOnce

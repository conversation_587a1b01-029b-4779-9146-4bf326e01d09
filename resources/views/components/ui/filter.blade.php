@props(['filters', 'activeFilter'])
@php
    $currentType = isset($activeFilter['filter']) && $activeFilter['filter'] ? $activeFilter['filter'] : 'hot';
@endphp

<div class="flex flex-wrap items-center gap-2" role="group" {{ $attributes }}>
    @foreach ($filters as $filter)
        <button @class([
            'filter-active' => $currentType === $filter['value'],
            'filter-btn [&.filter-active]:pointer-events-none text-white bg-neutral-650 rounded-[4px] flex cursor-pointer items-center px-4 py-2.5 justify-center gap-2 text-sm leading-5',
        ]) value="{{ $filter['value'] }}" data-value="{{ $filter['value'] }}">
            @if (isset($filter['icon']))
                <i class="icon-{{ $filter['icon'] }} pointer-events-none text-[20px] text-neutral-300"></i>
            @endif
            <span class="pointer-events-none text-nowrap text-xs capitalize xl:text-sm xl:leading-[18px]">{{ __($filter['name']) }}</span>
        </button>
    @endforeach
</div>

@php
    use App\Enums\UrlPathEnum;

    $swiperRequiredClass = 'events-casino-swiper';
    $swiperCasinoConfig = [
        'slidesPerView' => 2.9,
        'spaceBetween' => 8,
        'loop' => false,
        'navigation' => [
            'prevEl' => '.events-casino-swiper-prev',
            'nextEl' => '.events-casino-swiper-next',
        ],
        'breakpoints' => [
            '768' => [
                'slidesPerView' => 6,
                'spaceBetween' => 8,
            ],
            '1200' => [
                'slidesPerView' => 6,
                'spaceBetween' => 20,
            ],
        ],
    ];
@endphp

<div class="events-sports max-xl:px-4">
    <div class="sports-title mb-3 xl:mb-6 flex items-center justify-between">
        <div class="uppercase text-neutral text-[16px] xl:text-[24px] leading-[28px] xl:leading-[29px] font-redzone">
            Game áp dụng
        </div>
        <div class="flex items-center gap-4">
        <div class="section-header__nav flex items-center gap-1 max-xl:hidden">
                <div
                    @class([
                        "w-[24px] h-[24px] bg-neutral-600 rounded-lg flex items-center justify-center group xl:hover:brightness-150",
                        "events-casino-swiper-prev",
                        "[&.swiper-button-disabled]:pointer-events-none",
                    ])
                >
                    <i class="icon-arrow-left text-neutral text-[16px] group-[&.swiper-button-disabled]:text-neutral-500"></i>
                </div>
                <div
                    @class([
                        "w-[24px] h-[24px] bg-neutral-600 rounded-lg flex items-center justify-center group xl:hover:brightness-150",
                        "events-casino-swiper-next",
                        "[&.swiper-button-disabled]:pointer-events-none",
                    ])
                >
                    <i class="icon-arrow-right text-neutral text-[16px] group-[&.swiper-button-disabled]:text-neutral-500"></i>
                </div>
            </div>
            <x-kit.link-button style="light" href="{{ UrlPathEnum::CASINO->value }}" class="ml-auto flex items-center gap-1.5 capitalize group">
                Xem thêm
                <i class="icon-arrow-right group-hover:text-primary-300 text-primary text-[20px]"></i>
            </x-kit.link-button>
        </div>
    </div>

    <div class="casino-list-wrapper">
        <x-kit.swiper :autoplay="true" :swiperConfig="$swiperCasinoConfig" :swiperRequiredClass="$swiperRequiredClass" swiperWrapperClass="events-casino-swiper-game">
            @foreach ($casinoGames as $game)
                <div class="swiper-slide">
                    <x-ui.card 
                        type="casino"
                        :game="$game"
                        name="{{ $game->name ?? 'title' }}" 
                        image="{{ $game->image ?? '' }}" 
                        data-api="{{ $game->api_url ?? '' }}" 
                        id="{{ $game->partner_game_id ?? '' }}"
                        tableId="{{ $game->table_id ?? '' }}"
                        data-id="{{ $game->partner_game_id ?? '' }}"
                        favorite="{{ isset($game->is_favorite) && $game->is_favorite ? 'favorite' : '' }}"
                        provider="{{ $game->partner_txt ?? '' }}" 
                        partner="{{ $game->partner ?? '' }}"
                        tags="{{ $game->tags ?? '' }}"
                        class="text-marron loader-image-transparent flex flex-col items-center"
                        partner_provider="{{ $game->partner_provider ?? '' }}" 
                        isFavoriteCenter
                        cardLabelClass="max-xl:left-[8px]"
                        providerClass="!text-neutral-200"
                        jackpotClass="!text-[10px] xl:!text-xs leading-[15px]"
                        jackpotWrapperClass="!py-[1px] xl:!py-[5px] !px-[4px] xl:!px-[9.5px] !gap-[2px] xl:!gap-[4px] max-xl:rounded-bl-[8px] right-[-0.5px] max-xl:h-[18px]"
                    />
                </div>
            @endforeach
        </x-kit.swiper>
    </div>
</div>

@pushOnce('scripts')
<script>
    window.addEventListener('DOMContentLoaded', () => {
        const casinoGames = @json($casinoGames);
        if (casinoGames && casinoGames.length) {
            casinoGames.forEach(game => {
                const partner = game.partner;
                const partner_id = game.partner_game_id;
                window.openLiveStream(`${partner}_${partner_id}`);
            });
        }
    });
</script>
@endPushOnce

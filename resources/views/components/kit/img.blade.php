@props([
    'src' => '',
    'alt' => '',
    'errorSrc' => '',
    'className' => '',
    'width' => null,
    'height' => null,
    'sizes' => '100vw',
    'loading' => 'lazy',
    'priority' => false
])

@php
    $basePath = pathinfo($src, PATHINFO_DIRNAME);
    $filename = pathinfo($src, PATHINFO_FILENAME);
    $extension = pathinfo($src, PATHINFO_EXTENSION);

    // Generate modern format URLs
    $avifSrc = $basePath . '/' . $filename . '.avif';
    $webpSrc = $basePath . '/' . $filename . '.webp';

    // Check if files exist
    $avifExists = file_exists(public_path($avifSrc));
    $webpExists = file_exists(public_path($webpSrc));
@endphp

<div class="image-container relative">
    <div class="skeleton absolute inset-0 bg-gray-200 animate-pulse"></div>

    @if($avifExists || $webpExists)
        <picture>
            @if($avifExists)
                <source
                    @if($priority)
                        srcset="{{ asset($avifSrc) }}"
                    @else
                        data-srcset="{{ asset($avifSrc) }}"
                    @endif
                    type="image/avif"
                    @if($sizes) sizes="{{ $sizes }}" @endif
                >
            @endif

            @if($webpExists)
                <source
                    @if($priority)
                        srcset="{{ asset($webpSrc) }}"
                    @else
                        data-srcset="{{ asset($webpSrc) }}"
                    @endif
                    type="image/webp"
                    @if($sizes) sizes="{{ $sizes }}" @endif
                >
            @endif

            <img
                @if($priority)
                    src="{{ asset($src) }}"
                @else
                    data-src="{{ asset($src) }}"
                @endif
                alt="{{ $alt }}"
                data-error="{{ $errorSrc ? asset($errorSrc) : asset('asset/images/placeholder.webp') }}"
                class="lazy-image {{ $className }}"
                @if($width) width="{{ $width }}" @endif
                @if($height) height="{{ $height }}" @endif
                @if($loading === 'lazy' && !$priority) loading="lazy" @endif
                @if($sizes) sizes="{{ $sizes }}" @endif
                decoding="async"
            />
        </picture>
    @else
        <img
            @if($priority)
                src="{{ asset($src) }}"
            @else
                data-src="{{ asset($src) }}"
            @endif
            alt="{{ $alt }}"
            data-error="{{ $errorSrc ? asset($errorSrc) : asset('asset/images/placeholder.webp') }}"
            class="lazy-image {{ $className }}"
            @if($width) width="{{ $width }}" @endif
            @if($height) height="{{ $height }}" @endif
            @if($loading === 'lazy' && !$priority) loading="lazy" @endif
            @if($sizes) sizes="{{ $sizes }}" @endif
            decoding="async"
        />
    @endif
</div>

@pushOnce('scripts')
    @vite(['resources/js/lazy-image.js'])
@endpushOnce

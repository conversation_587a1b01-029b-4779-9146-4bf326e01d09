{{-- Critical CSS for above-the-fold content --}}
<style>
/* Critical CSS - Inline for fastest loading */
/* Reset and base styles */
*,*::before,*::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
*::before,*::after{--tw-content:''}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON>l,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
body{margin:0;line-height:inherit}

/* Layout */
.container{width:100%;margin-left:auto;margin-right:auto;padding-left:1rem;padding-right:1rem}
@media (min-width:575px){.container{padding-left:1rem;padding-right:1rem}}
@media (min-width:1272px){.container{max-width:1260px;padding-left:0.625rem;padding-right:0.625rem}}

/* Typography */
.font-sans{font-family:Inter,sans-serif}
.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}

/* Colors */
.bg-neutral-790{background-color:#13171C}
.text-neutral{color:#FFFFFF}
.text-primary{color:#05E093}

/* Layout utilities */
.relative{position:relative}
.absolute{position:absolute}
.inset-0{top:0;right:0;bottom:0;left:0}
.flex{display:flex}
.hidden{display:none}
.w-full{width:100%}
.h-full{height:100%}
.min-h-screen{min-height:100vh}

/* Header critical styles */
.header{position:fixed;top:0;left:0;right:0;z-index:50;background-color:#13171C}

/* Hero banner critical styles */
.hero-banner{aspect-ratio:370/110}
.hero-banner img{width:100%;height:100%;object-fit:cover}

/* Critical image loading */
.lazy-image{opacity:0;transition:opacity 0.3s ease-in-out}
.lazy-image.loaded{opacity:1}
.skeleton{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);background-size:200% 100%;animation:loading 1.5s infinite}

@keyframes loading{
    0%{background-position:200% 0}
    100%{background-position:-200% 0}
}

/* Critical responsive utilities */
@media (max-width:1198px){
    .xl\:hidden{display:none}
    .max-xl\:block{display:block}
}

@media (min-width:1199px){
    .xl\:block{display:block}
    .xl\:flex{display:flex}
    .max-xl\:hidden{display:none}
}

/* Critical spacing */
.p-4{padding:1rem}
.pt-6{padding-top:1.5rem}
.pb-6{padding-bottom:1.5rem}
.mb-6{margin-bottom:1.5rem}
.gap-6{gap:1.5rem}

/* Critical flexbox */
.flex-col{flex-direction:column}
.items-center{align-items:center}
.justify-center{justify-content:center}
.justify-between{justify-content:space-between}

/* Critical grid */
.grid{display:grid}
.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}
.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}
.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}

/* Critical borders and radius */
.rounded{border-radius:0.25rem}
.rounded-lg{border-radius:0.5rem}
.border{border-width:1px}

/* Critical shadows */
.shadow{box-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1),0 1px 2px -1px rgb(0 0 0 / 0.1)}

/* Critical transforms */
.scale-105{transform:scale(1.05)}
.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}

/* Critical overflow */
.overflow-hidden{overflow:hidden}

/* Critical z-index */
.z-10{z-index:10}
.z-20{z-index:20}
.z-50{z-index:50}

/* Critical text utilities */
.text-sm{font-size:0.875rem;line-height:1.25rem}
.text-base{font-size:1rem;line-height:1.5rem}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.font-medium{font-weight:500}
.font-semibold{font-weight:600}
.font-bold{font-weight:700}
.text-center{text-align:center}

/* Critical button styles */
.btn{display:inline-flex;align-items:center;justify-content:center;border-radius:0.5rem;font-weight:500;transition:all 0.2s}
.btn-primary{background-color:#05E093;color:#FFFFFF}
.btn-primary:hover{background-color:#02BC7B}

/* Critical form styles */
.form-input{width:100%;border-radius:0.5rem;border:1px solid #d1d5db;padding:0.5rem 0.75rem}
.form-input:focus{outline:none;border-color:#05E093;box-shadow:0 0 0 3px rgb(5 224 147 / 0.1)}

/* Critical loading states */
.loading{pointer-events:none;opacity:0.6}

/* Critical accessibility */
.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}

/* Critical focus styles */
.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}
.focus\:ring-2:focus{box-shadow:0 0 0 2px #05E093}

/* Critical hover states */
.hover\:scale-105:hover{transform:scale(1.05)}
.hover\:opacity-80:hover{opacity:0.8}

/* Critical animations */
.animate-pulse{animation:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite}
@keyframes pulse{
    0%,100%{opacity:1}
    50%{opacity:.5}
}

.animate-spin{animation:spin 1s linear infinite}
@keyframes spin{
    from{transform:rotate(0deg)}
    to{transform:rotate(360deg)}
}

/* Critical performance optimizations */
img{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}
.will-change-transform{will-change:transform}
.will-change-opacity{will-change:opacity}

/* Critical mobile optimizations */
@media (max-width:767px){
    .container{padding-left:0.5rem;padding-right:0.5rem}
    .text-sm{font-size:0.75rem}
    .p-4{padding:0.75rem}
}
</style>

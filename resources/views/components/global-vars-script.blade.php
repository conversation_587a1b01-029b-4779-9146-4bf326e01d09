@php
    $currentUser = Auth::user();
    $isLoggedIn = Auth::check();
    $isMobile = App::make('isMobile');
    $brandName = strtolower(config('app.brand_name'));

    $config = [
        'formRules' => config('validation.rules'),
        'validationMessages' => config('validation.messages'),
    ];
@endphp
<script>
    var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    var isLoggedIn = {{ $isLoggedIn ? 'true' : 'false' }};

    // Config variables
    const appConfig = @json($config);

    @if (Auth::check())
        @php
            $userHasWelcomePromotion = isset($currentUser?->package_id) && $currentUser?->package_id === config('constants.PROMOTION_PLAN_TYPE.WELCOME');
        @endphp
        var defaultPackageId = {{ config('constants.PROMOTION_PLAN_TYPE.COMMISSION') }};
        var currentUserPackageId = {{ empty($currentUser?->package_id) ? 'null' : $currentUser?->package_id }};

        var isWelcomePromotion = {{ $userHasWelcomePromotion ? 1 : 0 }};
        var globalBrandName = "{{ $brandName }}";
    @endif

    var globalSiteKey = "{{ config('cloudflare.turnstile.site_key') }}";
    var globalGoogleRecaptchaSiteKey = "{{ config('google.recaptcha.site_key') }}";
    const {
        formRules,
        validationMessages
    } = appConfig;

    var globalGoldenHourEventStartTime = "{{ config('app.golden_hour_start_time') }}"?.replace("_"," ");
    var globalGoldenHourEventEndTime = "{{ config('app.golden_hour_end_time') }}"?.replace("_"," ");
</script>

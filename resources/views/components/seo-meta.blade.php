@props([
    'title' => '',
    'description' => '',
    'keywords' => '',
    'image' => '',
    'url' => '',
    'type' => 'website',
    'siteName' => 'Z22VIVU',
    'locale' => 'vi_VN',
    'schemas' => []
])

@php
    $currentUrl = $url ?: url()->current();
    $defaultImage = asset('favicon.svg');
    $ogImage = $image ?: $defaultImage;
    $pageTitle = $title ?: config('app.brand_name');
    $pageDescription = $description ?: 'Premium gaming and entertainment platform';
@endphp

{{-- Basic SEO Meta Tags --}}
<title>{{ $pageTitle }}</title>
<meta name="description" content="{{ $pageDescription }}">
@if($keywords)
    <meta name="keywords" content="{{ $keywords }}">
@endif
<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
<meta name="googlebot" content="index, follow">
<link rel="canonical" href="{{ $currentUrl }}">

{{-- Open Graph Meta Tags --}}
<meta property="og:locale" content="{{ $locale }}">
<meta property="og:type" content="{{ $type }}">
<meta property="og:title" content="{{ $pageTitle }}">
<meta property="og:description" content="{{ $pageDescription }}">
<meta property="og:url" content="{{ $currentUrl }}">
<meta property="og:site_name" content="{{ $siteName }}">
<meta property="og:image" content="{{ $ogImage }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:type" content="image/svg+xml">

{{-- Twitter Card Meta Tags --}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $pageTitle }}">
<meta name="twitter:description" content="{{ $pageDescription }}">
<meta name="twitter:image" content="{{ $ogImage }}">
<meta name="twitter:site" content="@z22vivu">
<meta name="twitter:creator" content="@z22vivu">

{{-- Additional SEO Meta Tags --}}
<meta name="author" content="{{ $siteName }}">
<meta name="publisher" content="{{ $siteName }}">
<meta name="application-name" content="{{ $siteName }}">
<meta name="msapplication-TileColor" content="#05E093">
<meta name="msapplication-config" content="{{ asset('browserconfig.xml') }}">

{{-- Geo Meta Tags --}}
<meta name="geo.region" content="VN">
<meta name="geo.placename" content="Vietnam">
<meta name="geo.position" content="16.0583;108.2772">
<meta name="ICBM" content="16.0583, 108.2772">

{{-- Mobile Meta Tags --}}
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="apple-mobile-web-app-title" content="{{ $siteName }}">

{{-- Structured Data --}}
@if(count($schemas) > 0)
    @foreach($schemas as $schema)
        <script type="application/ld+json">
            {!! $schema !!}
        </script>
    @endforeach
@else
    {{-- Default Organization Schema --}}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ $siteName }}",
        "url": "{{ url('/') }}",
        "logo": "{{ $defaultImage }}",
        "description": "{{ $pageDescription }}",
        "sameAs": [
            "https://facebook.com/z22vivu",
            "https://twitter.com/z22vivu",
            "https://instagram.com/z22vivu"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Vietnamese", "English"]
        }
    }
    </script>

    {{-- Website Schema --}}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "{{ $siteName }}",
        "url": "{{ url('/') }}",
        "description": "{{ $pageDescription }}",
        "inLanguage": "vi-VN",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ url('/search') }}?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
@endif

{{-- Preload critical resources --}}
<link rel="preload" href="{{ asset('build/assets/app-CrTTsayB.css') }}" as="style">
<link rel="preload" href="{{ asset('build/assets/vendor-ui-CSbci_z6.js') }}" as="script">

{{-- DNS Prefetch for external resources --}}
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.bunny.net">
<link rel="dns-prefetch" href="//www.google.com">
<link rel="dns-prefetch" href="//code.jquery.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//cdn.socket.io">

{{-- Preconnect to critical third-party origins --}}
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://fonts.bunny.net" crossorigin>

{{-- Resource hints for better performance --}}
<link rel="prefetch" href="{{ asset('build/assets/modal-DB3lvT2D.js') }}">
<link rel="prefetch" href="{{ asset('build/assets/sports-DTef5jHe.js') }}">

{{-- Security headers --}}
<meta http-equiv="X-Content-Type-Options" content="nosniff">
<meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
<meta http-equiv="X-XSS-Protection" content="1; mode=block">
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

{{-- Performance hints --}}
<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width">
<meta name="color-scheme" content="dark light">

{{-- Favicon and app icons --}}
<link rel="icon" type="image/svg+xml" href="{{ asset('favicon.svg') }}">
<link rel="alternate icon" href="{{ asset('favicon.ico') }}">
<link rel="apple-touch-icon" href="{{ asset('apple-touch-icon.png') }}">

{{-- Language alternatives --}}
<link rel="alternate" hreflang="vi" href="{{ url('/') }}">
<link rel="alternate" hreflang="en" href="{{ url('/en') }}">
<link rel="alternate" hreflang="x-default" href="{{ url('/') }}">

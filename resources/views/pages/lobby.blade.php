<x-layout>
    <div class="grid">
        <div class="grid gap-2 xl:flex justify-between items-center">
            <div id="type-buttons">
                @foreach ($gameFilter['typeOptions'] as $filter)
                    <button
                        class="type-btn cursor-pointer p-2 hover:brightness-110 rounded-lg {{ $activeFilter['type'] === $filter['value'] ? 'bg-yellow-400 text-white' : 'bg-grey-200 text-black' }}"
                        value="{{ $filter['value'] }}">
                        {{ $filter['name'] }}
                    </button>
                @endforeach
            </div>
            <div id="filter-buttons">
                @foreach ($gameFilter['sortOptions'] as $filter)
                    <button
                        class="filter-btn cursor-pointer p-2 hover:brightness-110 rounded-lg {{ $activeFilter['filter'] === $filter['value'] ? 'bg-yellow-400 text-white' : 'bg-grey-200 text-black' }}"
                        value="{{ $filter['value'] }}">
                        {{ __($filter['i18_key']) }}
                    </button>
                @endforeach
            </div>
            <div>
                <x-ui.search-input />
            </div>
        </div>
        @if (!empty($data?->data?->items))
            <div id="game-container" class="grid gap-6 grid-cols-2 md:grid-cols-4 xl:grid-cols-4 xl:gap-8 py-2">
                @foreach ($data?->data?->items as $item)
                    <x-ui.card
                        :game="$item"
                        name="{{ $item->name ?? 'title' }}"
                        image="{{ $item->image ?? '' }}"
                        type="game"
                        data-api="{{ $item->api_url ?? '' }}"
                        favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                        provider="{{ $item->partner_txt ?? '' }}"
                        partner="{{ $item->partner ?? '' }}"
                        class="flex flex-col items-center text-marron loader-image-transparent"
                        id="{{ $item->partner_game_id ?? '' }}"
                        tableId="{{ $item->table_id ?? '' }}"
                    ></x-ui.card>
                @endforeach
            </div>

            <div class="flex justify-center">
                <button id="loadmore" type="button"
                    class="cursor-pointer p-2 bg-yellow-400 hover:brightness-110 rounded-lg">
                    {{ __('common.loadmore') }}
                </button>
            </div>
        @endif
    </div>

    @push('scripts')
        <script>
            const gameFilter = @json($gameFilter);
            const initActiveFilter = @json($activeFilter);

            const dataEndpoint = "/game/search";

            // at page load
            let activeFilter = {
                page: 1,
                limit: gameFilter.limit,
            };

            window.addEventListener("load", (event) => {

                const jsonDataResponse = @json($data);
                const initData = jsonDataResponse.data.items;

                if (initData.length < activeFilter.limit) {
                    $("#loadmore").toggle(false);
                }

                $("#searchKeyword").val(initActiveFilter.keyword);
                $("#clearKeyword").toggle(!!initActiveFilter.keyword);

                const clearActiveStates = (selector) => {
                    const filterButtons = document.querySelectorAll(selector);
                    filterButtons.forEach(btn => {
                        btn.classList.remove('bg-yellow-400', 'text-white');
                        btn.classList.add('bg-grey-200', 'text-black');
                    });
                }

                const setActiveState = (button) => {
                    if (button) {
                        button.classList.add('bg-yellow-400', 'text-white');
                        button.classList.remove('bg-grey-200', 'text-black');
                    }
                }


                const createElement = (game) =>
                    `<x-ui.card
                        name="${game.name}"
                        type="game"
                        game="${game}"
                        image="${game.image}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        tableId="${game.table_id ?? ''}"
                        favorite="${game.is_favorite ? 'favorite' : ''}"
                        provider="${game.partner_txt}"
                        partner="${game.partner}"
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;

                const pushKeywordStateDebounced = debounce((keyword) => {
                    pushState({
                        keyword
                    });
                });

                // must invoke this on change value
                $("#searchKeyword").on("input", function(e) {
                    $("#clearKeyword").toggle(!!e.target.value);
                    activeFilter.page = 1;
                    pushKeywordStateDebounced(e.target.value)
                });

                $("#clearKeyword").on("click", () => {
                    $("#clearKeyword").toggle(false);
                    pushState({
                        keyword: "",
                    });
                });

                const fetchGames = async (queryParams) => {
                    try {
                        const data = await fetchData(dataEndpoint, {
                            keyword: queryParams.keyword,
                            page: queryParams.page,
                            limit: queryParams.limit,
                            filter: queryParams.filter,
                            type: queryParams.type,
                        });

                        const {
                            items,
                            page: currentPage,
                            totalPage
                        } = data.data;

                        const loadmoreDisplay = queryParams.page < totalPage;

                        $("#loadmore").prop("disabled", false).toggle(loadmoreDisplay);

                        //  render response
                        if (queryParams.page === 1) {
                            $("#game-container").empty();
                        }

                        items.forEach((game) => {
                            const ele = createElement(game);
                            $("#game-container").append(ele);
                        });

                        // update jackpot value from websocket
                        updateJackpotValue();
                    } catch (error) {
                        // TODO replace with popup
                        console.log(error.message);
                    }
                };

                const fetchGamesDebounced = debounce(async (filter) => {
                    await fetchGames(filter);
                }, 500);

                window.addEventListener("pushstate", function(event) {
                    activeFilter.page = 1;
                    updatedFilterUI({
                        type: event.detail.state.type,
                        filter: event.detail.state.filter,
                        keyword: event.detail.state.keyword
                    })

                    const queryParams = {
                        ...activeFilter,
                        ...event.detail.state,
                    };
                    fetchGamesDebounced(queryParams);
                });

                window.addEventListener("popstate", function(event) {
                    activeFilter.page = 1;
                    updatedFilterUI({
                        type: event.state.type,
                        filter: event.state.filter,
                        keyword: event.state.keyword
                    })
                    const queryParams = {
                        ...activeFilter,
                        ...event.state,
                    };
                    fetchGamesDebounced(queryParams);
                });

                const updatedFilterUI = ({
                    type,
                    filter,
                    keyword
                }) => {
                    $("#searchKeyword").val(keyword);
                    $("#clearKeyword").toggle(!!keyword);

                    clearActiveStates('#type-buttons .type-btn')
                    const currentActiveTypeBtn = document.querySelector(`#type-buttons .type-btn[value="${type}"]`);
                    setActiveState(currentActiveTypeBtn)

                    clearActiveStates('#filter-buttons .filter-btn')
                    const currentActiveFilterBtn = document.querySelector(
                        `#filter-buttons .filter-btn[value="${filter}"]`);
                    setActiveState(currentActiveFilterBtn)
                }
            });
        </script>
    @endpush
</x-layout>

<?php

namespace Modules\RewardGoldenHour\Services;

use App\Services\GatewayApi;
use App\Services\FakerApi;
use Symfony\Component\HttpFoundation\Response;
use App\Services\AccountService;
use Illuminate\Support\Facades\App;
use Exception;

class RewardGoldenHourService
{
    private GatewayApi $gatewayApi;
    private FakerApi $fakerApi;
    private AccountService $accountService;

    public function __construct(GatewayApi $gatewayApi, FakerApi $fakerApi, AccountService $accountService)
    {
        $this->gatewayApi = $gatewayApi;
        $this->fakerApi = $fakerApi;
        $this->accountService = $accountService;
    }

    public function getRewardList()
    {
        $endpoint = 'lixi/list';
        if (App::environment('local')) {
            $response = $this->fakerApi->getPromotion($endpoint);
        } else {
            $response = $this->gatewayApi->getPromotion($endpoint);   
        }

        if (isset($response->code) && $response->code === Response::HTTP_OK) {
            $rewardList = $response->data;
            $rewardTotal = $response->total_txt ?? '0/200';
            $rewardTotalParts = explode('/', $rewardTotal);
            $maximumAward = isset($rewardTotalParts[1]) ? (int)$rewardTotalParts[1] : 200;
            $remainingAward = $maximumAward - ($response->total ?? 0);
            return [
                'reward_list' => $rewardList,
                'reward_total' => $rewardTotal,
                'remaining_award' => $remainingAward,
            ];
        }

        return [];
    }
    public function getUserTurnover()
    {
        try {
            $eventKey = config('rewardgoldenhour.key');
            $endpoint = 'account/info';
            if (App::environment('local')) {
                $response = $this->fakerApi->get($endpoint);
            } else {
                $response = $this->gatewayApi->get($endpoint);
            }
            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                $userTurnover = $response->data;
                if (isset($userTurnover->rolling_info) && $userTurnover->rolling_info) {
                    $rollingInfo = array_filter($userTurnover->rolling_info, function ($item) use ($eventKey) {
                        return $item->event_id === $eventKey;
                    });
                    $rollingInfo = array_map(function ($item) use ($userTurnover) {
                        $item->turnover = $userTurnover->turnover;
                        $item->rolling_main = $userTurnover->rolling;
                        return $item;
                    }, $rollingInfo);
                    return $rollingInfo;
                }
                return null;
            }
        } catch (Exception $e) {
            return null;
        }
    }

}
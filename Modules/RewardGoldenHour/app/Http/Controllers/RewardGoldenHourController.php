<?php

namespace Modules\RewardGoldenHour\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\RewardGoldenHour\Services\RewardGoldenHourService;
use function App\Helpers\generateSeoMetaData;
use Illuminate\Http\Request;
class RewardGoldenHourController extends Controller
{
    protected $rewardGoldenHourService;

    public function __construct(RewardGoldenHourService $rewardGoldenHourService)
    {
        $this->rewardGoldenHourService = $rewardGoldenHourService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        generateSeoMetaData();

        $userTurnover = null;
        $rewardList = $this->rewardGoldenHourService->getRewardList();

        $userCookie = $request->cookie('user');
        if ($userCookie || Auth::check()) {
            $userTurnover = $this->rewardGoldenHourService->getUserTurnover();
        }

        return view('rewardgoldenhour::index', compact('rewardList', 'userTurnover'));
    }
    
}

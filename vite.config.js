import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import { terser } from "rollup-plugin-terser";
import viteCompression from "vite-plugin-compression";
import purgecss from "vite-plugin-purgecss";
import * as glob from "glob";

const jsFiles = glob.sync("resources/js/**/*.js");

export default defineConfig({
    server: {
        host: "127.0.0.1", // Allow access from any IP address
        port: 8000,
    },
    build: {
        target: 'es2015',
        cssCodeSplit: true,
        sourcemap: false,
        minify: 'terser',
        rollupOptions: {
            output: {
                manualChunks(id) {
                    // Vendor chunking strategy for better caching
                    if (id.includes('node_modules')) {
                        const directories = id.split('/');
                        const name = directories[directories.lastIndexOf('node_modules') + 1];

                        // Group common libraries together
                        if (['swiper'].includes(name)) {
                            return 'vendor-ui';
                        }
                        if (['toastify-js'].includes(name)) {
                            return 'vendor-utils';
                        }
                        return `vendor-${name}`;
                    }

                    // Split large application modules
                    if (id.includes('resources/js/')) {
                        if (id.includes('auth') || id.includes('modal')) {
                            return 'app-auth';
                        }
                        if (id.includes('game') || id.includes('sports')) {
                            return 'app-games';
                        }
                        if (id.includes('account') || id.includes('deposit') || id.includes('withdraw')) {
                            return 'app-account';
                        }
                    }
                },
                // Optimize chunk file names for better caching
                chunkFileNames: 'assets/[name]-[hash].js',
                entryFileNames: 'assets/[name]-[hash].js',
                assetFileNames: 'assets/[name]-[hash].[ext]'
            },
        },
    },
    plugins: [
        laravel({
            input: [
                "resources/sass/app.scss",
                "resources/js/app.js",
                "resources/js/game-filters.js",
                "resources/js/account-filter.js",
                "resources/js/sports.js",
                "resources/js/deposit/codepay.js",
                "resources/js/deposit/crypto.js",
                "resources/js/deposit/card.js",
                "resources/js/deposit/ewallet.js",
                "resources/js/account/information.js",
                "resources/js/account/change-password.js",
                "resources/js/account/info-tab.js",
                "resources/js/collapse.js",
                "resources/js/account/bank-management.js",
                "resources/js/account/overview.js",
                "resources/js/search-input.js",
                "resources/js/withdraw/bank.js",
                "resources/js/withdraw/card.js",
                "resources/js/withdraw/crypto.js",
                "resources/js/deposit/tab.js",
                "resources/js/games.js",
                "resources/js/home/<USER>",
                "resources/js/game/card.js",
                "resources/js/types-filter.js",
                'resources/js/lazy-image.js',
                "resources/js/deposit/index.js",
                "resources/js/account/promotion.js",
                "resources/js/withdraw/index.js",
                "resources/js/dropdown-bank.js",
                "resources/js/notification.js",
                "resources/js/notification-mb.js",
                "resources/js/mb-game-card.js",
                "resources/js/deposit/suggest-deposit.js",
                "resources/js/events/index.js",
                "resources/js/popup.js",
                "resources/js/vip.js",
                "resources/js/affiliate.js",
                ...jsFiles,
            ],
            refresh: true,
        }),
        terser({
            compress: {
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ['console.log', 'console.info', 'console.debug'],
                passes: 2,
            },
            mangle: {
                safari10: true,
            },
            format: {
                comments: false,
                ecma: 2015,
            },
        }),
        viteCompression({
            algorithm: 'gzip',
            ext: '.gz',
            threshold: 1024,
            deleteOriginFile: false,
        }),
        viteCompression({
            algorithm: 'brotliCompress',
            ext: '.br',
            threshold: 1024,
            deleteOriginFile: false,
        }),
        purgecss({
            content: [
                './resources/**/*.html',
                './resources/**/*.scss',
                './resources/views/**/*.blade.php', // Ensure Blade templates are included
            ],
            defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
            safelist: [
                // Use a regular expression to match all classes
                /^.*$/,
            ],
        }),
    ],
});

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = config('app.locale', 'en');
        // Check if a 'lang' parameter exists in the request
        if ($request->route('locale')) {
            if (in_array($request->route('locale'), config('app.available_locales', ['en']))) { // Supported languages
                $locale = $request->route('locale'); // override default locale
                Cookie::queue('lang', $locale, 60 * 24 * 30); // Set cookie for 30 days
            }
        } else {
            Cookie::queue('lang', $locale, 60 * 24 * 30); // Set cookie for 30 days
        }

        App::setLocale($locale); // Apply locale
        $localesConfig = config('constants.locales');

        if (array_key_exists($locale, $localesConfig)) {
            $localeConfig = $localesConfig[$locale];
        } else {
            $localeConfig = $localesConfig['en']; // Fallback to default locale config
        }

        View::share([
            'localeConfig' => $localeConfig,
        ]);

        $request->merge([
            'lang' => $locale,
        ]);

        return $next($request);
    }
}

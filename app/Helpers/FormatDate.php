<?php

if (! function_exists('format_date')) {
    function format_date($date, $format = 'd/m/Y H:i', $timezone = 'UTC') {
        if (!$date) {
            return null; 
        }
        
        try {
            $dateTime = new DateTime($date, new DateTimeZone('UTC'));
            $dateTime->setTimezone(new DateTimeZone($timezone)); 
            return $dateTime->format($format); 
        } catch (Exception $e) {
            return null; 
        }
    }
    
}

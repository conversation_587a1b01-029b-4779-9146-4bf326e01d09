<?php

namespace App\Http\Controllers\HomeController;

use App\Enums\GatewayEndpoint;
use App\Helpers\DetectDeviceHelper;
use App\Http\Controllers\Controller;
use App\Services\CasinoService;
use App\Services\GameService;
use Illuminate\Http\Request;
use App\Services\GatewayApi;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use App\Services\AuthService;
use App\Services\SportsService;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;
use Jenssegers\Agent\Agent;

class HomeController extends Controller
{
    // TODO: move it to Enums - LobbiesEnum
    const NOHU_TYPE = 'nohu';
    const SORT_ALL = 'all';

    // Api gateway service
    /**
     * @var GatewayApi
     */
    protected $GatewayApi;

    /**
     * @var AuthService
     */
    protected $authService;

    /**
     * @var GameService
     */
    private GameService $gameService;
    /**
     * @var CasinoService
     */
    private CasinoService $casinoService;
    /**
     * @var SportsService
     */
    private $sportsService;

    /**
     * HomeController constructor.
     * @param GatewayApi $GatewayApi
     * @param AuthService $authService
     * @param GameService $gameService
     * @param SportsService $sportsService
     */
    public function __construct(GatewayApi $GatewayApi, AuthService $authService, GameService $gameService, CasinoService $casinoService, SportsService $sportsService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->authService = $authService;
        $this->gameService = $gameService;
        $this->casinoService = $casinoService;
        $this->sportsService = $sportsService;
    }

    public function index(Request $request)
    {

        $token = $request->query('token');

        if ($token) {
            $result = $this->authService->loginWithToken($request, $token);
            if ($result['status']) {
                $result['status'] = 'OK';
                return redirect()->route('en.home.index')->withCookie($result['cookie']);
            } else {
                $request->session()->flash('TokenExpired', 'Không tìm thấy người dùng');
            }
        } else {
            Log::info('No token found in the URL.');
        }
        //config SEO
        $seo = generateSeoMetaData('home');
        $seo->schemas = [

            generateJsonLd("Organization", [
                "name" => $seo->title,
                "url" =>  url()->current(),
                "logo" => asset('favicon.svg'),
                "contactPoint" => [
                    "@Type" => "",
                    "telephone" => "",
                    "contactType" => "",
                    "email" => ""
                ],
                "address" => [
                    "@type" => "",
                    "streetAddress" => "",
                    "addressLocality" => "",
                    "addressRegion" => "",
                    "postalCode" => "",
                    "addressCountry" => ""
                ]
            ]),

            generateJsonLd("WebSite", [
                "url" => route('en.home.index'),
                "name" => $seo->title,
                "description" => $seo->description,
                "potentialAction" => [
                    "@type" => "",
                    "target" => "",
                    "query-input" => "",
                ]
            ])
        ];

        $sportSwiperConfig = [
            'slidesPerView' => 3,
            'spaceBetween' => 6,
            'loop' => true,
        ];

        $preLoadImg = asset("asset/images/home/<USER>/banner-welcome-mb.webp");
        $responses = $this->GatewayApi->getMultiple([
            // Get the LiveStream Games
            [
                'queryparams' => ['limit' => 6, 'partner' => 'vingame,rik,go,b52', 'sort' => 'hot', 'page' => 1],
                'endpoint' => GatewayEndpoint::CASINO_SEARCH->value,
            ],
            // Get the NoHu Games
            [
                'queryparams' => ['limit' => 8, 'type' => self::NOHU_TYPE, 'sort' => self::SORT_ALL, 'page' => 1],
                'endpoint' => GatewayEndpoint::GAME_SEARCH->value,
            ],
            [
                'endpoint' => GatewayEndpoint::HOT_MATCHES->value,
                'type' => GatewayApi::PROMOTION_PREFIX,
            ],
            [
                'endpoint' => GatewayEndpoint::SLOT_JACKPOT->value,
            ]
        ]);
        $data = array_map(function ($response) {
            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                return $response->data ?? [];
            }
            return [];
        }, $responses);

        $streamGames = $data[0];
        $nohuGames = $data[1];
        $hotMatches = $data[2];
        $jackpotData = (array) $data[3];
        $totalJackpot = $this->gameService->getTotalJackpot($jackpotData);
        
        if (is_object($streamGames) && isset($streamGames->items) && $jackpotData) {
            $streamGames->items = $this->gameService->mergeJackpotValue($streamGames->items, $jackpotData);
        }
        if (is_object($nohuGames) && isset($nohuGames->items) && $jackpotData) {
            $nohuGames->items = $this->gameService->mergeJackpotValue($nohuGames->items, $jackpotData);
        }

        // Get live casino thumbs
        $liveCasinoThumbs = config('games.liveCasinoThumbs', []);
        $specialLabels = config('games.specialLabels', []);
        if (is_object($streamGames) && isset($streamGames->items)) {
            $streamGames->items = array_map(function ($item) use ($liveCasinoThumbs, $specialLabels) {
                $thumbId = mb_strtolower($item->partner_provider.'_'.$item->partner_game_id);
                if (in_array($thumbId, $liveCasinoThumbs)) {
                    $item->image = asset('asset/images/home/<USER>/' . $thumbId . '.webp');
                    $item->image_mobile = asset('asset/images/home/<USER>/' . $thumbId . '_mb.webp');
                }
                if (!empty($specialLabels) && isset($specialLabels[$thumbId])) {
                    $item->specialLabel = $specialLabels[$thumbId];
                }
                return $item;
            }, $streamGames->items);
        }

        return view('pages.home',
            [
                'streamGames' => $streamGames->items ?? [],
                'nohuGames' => $nohuGames->items ?? [],
                'sportSwiperConfig' => $sportSwiperConfig,
                'nohuList' => $nohuGames,
                'hotMatches' => $hotMatches,
                "preLoadImg" => $preLoadImg,
                "totalJackpot" => $totalJackpot
            ]
        );
    }
}
